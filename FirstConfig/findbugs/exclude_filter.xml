<?xml version="1.0" encoding="utf-8"?>
<FindBugsFilter>
    <Match>
        <!--filter Google jar-->
        <Or>
            <Package name="~android\.support\.design.*" />
            <Package name="~android\.support\.v7.*" />
            <Package name="~android\.support\.v4.*" />
            <Package name="~android\.support\.test.*"/>
        </Or>
    </Match>

    <Match>
        <!--filter auto generate class-->
        <Or>
            <Class name="~.*\.*id*"/>
            <Class name="~.*\.*styleable*"/>
            <Class name="~.*\.*attr*"/>
            <Class name="~.*\.*drawable*"/>
            <Class name="~.*\.*menu*"/>
            <Class name="~.*\.*color*"/>
            <Class name="~.*\.*string*"/>
            <Class name="~.*\.*layout*"/>
            <Class name="~.*\.*mipmap*"/>
            <Class name="~.*\.*anim*"/>
            <Class name="~.*\.*bool*"/>
            <Class name="~.*\.*integer*"/>
            <Class name="~.*\.*style*"/>
            <Class name="~.*\.*animator*"/>
            <Class name="~.*\.*raw*"/>
        </Or>
    </Match>
    <Match>
        <!-- filter ExampleInstrumentedTest -->
        <!-- All bugs in test classes, except for JUnit-specific bugs -->
        <Class name="~.*\.*Test" />
        <Not>
            <Bug code="IJU" />
        </Not>
    </Match>

    <Match>
        <Or>
            <Package name="~com\.ainirobot\.firstconfig\.httpclient.*"/>
        </Or>
    </Match>

    <Match>
        <!--Filed pattern，class  -->
        <Or>
            <Class name="~com\.ainirobot\.firstconfig\.FirstConfigApplication"/>
            <Class name="~com\.ainirobot\.firstconfig\.ExampleInstrumentedTest"/>
        </Or>
    </Match>

    <Match>
        <!--filter bugs pattern below-->
        <Or>
            <Bug pattern="RV_RETURN_VALUE_IGNORED_BAD_PRACTICE"/>
            <Bug pattern="RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE"/>
            <Bug pattern="DC_DOUBLECHECK"/>
            <Bug pattern="RR_NOT_CHECKED"/>
            <Bug pattern="DM_DEFAULT_ENCODING"/>
            <Bug pattern="URF_UNREAD_FIELD"/>
            <Bug pattern="NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE"/>
        </Or>
    </Match>


    <!--filter reference: http://findbugs.sourceforge.net/manual/filter.html-->
    <!--bugs pattern reference: http://findbugs.sourceforge.net/bugDescriptions.html-->

</FindBugsFilter>