apply plugin: 'com.android.application'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    defaultConfig {
        applicationId "com.ainirobot.firstconfig"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 10500
        versionName "1.5.0"
        testInstrumentationRunner rootProject.ext.testInstrumentationRunner
    }

    signingConfigs { // need to in front of buildTypes
        signConfig {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file('keystore/android.keystore')
            storePassword 'android'
        }

        signConfig_845 {
            keyAlias 'AiniBox'
            keyPassword 'AiniRobot@9102'
            storeFile file('platform.keystore')
            storePassword 'AiniRobot@9102'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.signConfig_845
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.signConfig_845
            debuggable true
        }
	    release_821 {
            signingConfig signingConfigs.signConfig
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug_821 {
            signingConfig signingConfigs.signConfig
            debuggable true
    	}
    }

    dataBinding{
        enabled = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        disable 'MissingTranslation'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.squareup.okhttp3:okhttp:3.5.0'
    implementation 'org.simple:androideventbus:1.0.5'
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'

}
