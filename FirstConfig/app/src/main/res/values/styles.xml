<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowTranslucentStatus">true</item>
    </style>


    <style name="Theme.Start" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!--Dialog背景全透明无边框theme -->
    <style name="TitleStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">60px</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginStart">140px</item>
        <item name="android:layout_marginTop">125px</item>
    </style>

    <style name="ButtonStyle">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_width">640px</item>
        <item name="android:layout_height">108px</item>
        <item name="android:background">@drawable/language_btn_bg_layout_land</item>
        <item name="android:layout_marginBottom">122px</item>
        <item name="android:textSize">40px</item>
    </style>

</resources>
