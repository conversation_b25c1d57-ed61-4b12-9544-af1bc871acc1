<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Initial Setup</string>
    <string name="wx_qr_code_title">Activation by Scanning</string>
    <string name="wx_qr_code_tts">Please scan the QR Code with <PERSON>cha<PERSON> to activate me</string>
    <string name="wx_qr_code_loading">QR Code Requesting…</string>
    <string name="wx_qr_code_loading_success">Please scan the QR Code to activate the robot</string>
    <string name="wx_qr_code_loading_fail">QR Code requesting failed, please tap "Request Again"</string>
    <string name="wx_qr_code_load_retry">Retry</string>
    <string name="wx_qr_code_load_retry_again">Request Again</string>
    <string name="wx_qr_code_load_retry_again_des">QR Code Expired</string>
    <string name="disconnect_dialog_title">Network is not connected in current location</string>
    <string name="disconnect_dialog_content">Please check network settings</string>
    <string name="disconnect_dialog_button">Networking Settings</string>
    <string name="bind_qr_code_tts_vase">The robot has been successfully activated, I am %1$s</string>
    <string name="bind_success_title">Congratulations! Bind successful. The robot has been activated</string>
    <string name="bind_tts">I have been successfully activated, I am %1$s. Please help me with creating maps. </string>
    <string name="bind_tts_1">Activation succeeded. I am %1$s</string>
    <string name="bind_loading">Logging in…</string>
    <string name="bind_success_title_des">Sync Map from Cloud to Local</string>
    <string name="sync_map_start">Syncing Map …</string>
    <string name="sync_map_start_tts">Sync Processing</string>
    <string name="sync_map_fail">Syncing Map Failed</string>
    <string name="sync_map_fail_des">Please tap "Resync" on the cell phone to resync the existing map, or you can create a new map</string>
    <string name="sync_map_success">Syncing Map Successfully</string>
    <string name="sync_map_success_des">The robot has to be restarted manually to apply leading functions.</string>
    <string name="sync_map_success_relocale">Reposition Manually</string>
    <string name="login_error_title">Error Occurs</string>
    <string name="login_error_title_des">Please contact customer service for help</string>
    <string name="login_error_hwid_error">Error Message: Log in error (HWID)</string>
    <string name="login_error_robot_not_exist">Error Info:SN does not exist</string>
    <string name="bind_success_has_map">Apply</string>
    <string name="bind_success_create_map">Add New Map</string>
    <string name="estimate_failed">Positioning failed, please try again</string>
    <string name="set_charge_pile_success">Charging Pole Setting Successfully</string>
    <string name="set_charge_pile_fail">Charging Pole Setting Failed</string>
    <string name="start_auto_charge">Start Auto Charging</string>
    <string name="auto_charge_success">Auto Charging Successfully</string>
    <string name="auto_charge_fail">Auto Charging Failed</string>
    <string name="network_reset_success">Network Recovery Successful</string>
    <string name="bind_code_for_pc">6 digit binding code on computer:</string>
    <string name="sn_number_for_pc">Last 6 digits of Robot SN:</string>
    <string name="time_zone_select">Select Time Zone</string>
    <string name="area_zone_select">Select Region</string>
    <string name="back">Back</string>
    <string name="next1">Next</string>
    <string name="self_inspection_service_number">Customer HotLine：400&#8211;898&#8211;7779</string>
    <string name="inspect_err_title">Oops, the robot is in trouble. \n Please take pictures and send to customer service for help.</string>
    <string name="inspect_err_navigation_log_id">Error ID：\n</string>
    <string name="inspect_err_robot_sn">Robot SN：\n</string>
    <string name="inspect_err_feedback_tips">After giving feedback, \n you can try to recover the robot by restarting manually</string>
    <string name="inspect_err_qr_code_tips">After taking a photo or scanning the QR Code for feedback, \n you can try to recover the robot by restarting manually</string>
    <string name="inspect_err_after_sales_number">Customer HotLine：400&#8211;898&#8211;7779</string>
    <string name="inspect_err_error">Error Info</string>
    <string name="inspect_err_speech_tips">Oops, the robot is in trouble. \n Please take pictures and send to customer service for help.</string>
    <string name="self_inspection_speech">Starting Up… Please Wait…</string>
    <string name="china_choose_language">Language</string>
    <string name="choose_language">Selected Language：</string>
    <string name="simplified_chinese">Simplified Chinese</string>
    <string name="hint_time_zone">Time Zone</string>
    <string name="next">Next</string>
    <string name="reposition_state_ok">Positioning state is normal. No need to reposition again</string>
    <string name="reposition_reset_failed">Repositioning Failed</string>
    <string name="reposition_reset_remind_msg">Let the robot back onto the charging pole</string>
    <string name="reposition_reset_success">Repositioning Successful</string>
    <string name="debug_err_msg_checking_100times">Requesting the binding state has yielded no results 100 times.</string>
    <string name="debug_err_msg_qrcode_parser_fail">An error has occurred in the QR Code that was returned from the server.</string>
    <string name="debug_err_msg_bind_state_unknown">Remote control module error. Unknown binding state.</string>
    <string name="debug_err_msg_bind_state_null">Remote control module error. Binding information is empty.</string>
    <string name="bind_robot">Activate Robot</string>
    <string name="bind_robot_prompt">Please log in to %1$s to start the activation process.</string>
    <string name="last_sn">Last 6 digits of robot SN</string>
    <string name="binding_code">Binding Code</string>
    <string name="binding_code_expired">Binding Code Expired</string>
    <string name="refresh_code">Refresh Code</string>
    <string name="binding_code_loading">Acquiring binding code…</string>
    <string name="inspect_err_btn_shutdown">Shut down</string>
    <string name="inspect_err_qr_code_tips_saiph">You can shut it down first\nthen restart it.</string>
    <string name="inspect_err_feedback_tips_saiph">You can shut it down first, then restart it.</string>
    <string name="cloud_server_select">Cloud Server</string>
    <string name="cloud_europe">Europe Cloud</string>
    <string name="cloud_us_west">US West Cloud</string>
    <string name="cloud_other_regions">Cloud for other regions</string>
    <string name="cloud_tips">Please choose the right server that is compliant with your Enterprise Account region. Connect with technical support if you need help.</string>
    <string name="cloud_current">Current cloud server: %1$s</string>
    <string name="cloud_not_this"><u>Not this?</u></string>
    <string name="cloud_reboot">REBOOT</string>
    <string name="cloud_select_ok">OK</string>
    <string name="cloud_select_cancel">Cancel</string>
    <string name="server_language_error">Your Enterprise settings specify that the default language is %1$s, which is not yet supported by the current version.Please contact technical support.</string>
    <string name="activation_exception">Activation failed, please try again</string>
    <string name="settings_scenario_title">Select Running Scene</string>
    <string name="settings_scenario_subtitle">Please select the robot usage scenario</string>
    <string name="finish_active">Activation complete</string>
    <string name="loading">Loading…</string>
    <string name="activating_reboot">Activating, the robot will be restarted soon</string>
    <string name="skip">Skip</string>
    <string name="current_scene_empty">The current scene list is empty</string>
    <string name="data_exception_retry">Data loading failed, please try again</string>
    <string name="got_it">Got it！</string>
    <string name="woowa_title_tips">Tips</string>
    <string name="enable_one_time_password">After device restart, the three-finger pull-down dynamic password will be enabled，</string>
    <string name="be_careful">Please be careful</string>
    <string name="cloud_jp_server">Japan Cloud</string>
    <string name="lxc_repair">LXC repair</string>
    <string name="os_type_select">OS Type</string>
    <string name="os_robot">RobotOS</string>
    <string name="os_agent">AgentOS</string>
</resources>
