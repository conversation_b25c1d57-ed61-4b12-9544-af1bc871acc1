<resources xmlns:tools="http://schemas.android.com/tools">

    <string name="wifi_speed_kb">KB/s</string>
    <string name="wifi_speed_mb">MB/s</string>

    <string name="wifi_no_data">N/A</string>
    <string name="net_type_2G">2G</string>
    <string name="net_type_3G">3G</string>
    <string name="net_type_4G">4G</string>

    <string name="app_used_data_M">MB</string>
    <string name="app_used_data_G">GB</string>
    <string name="app_used_data_K">KB</string>
    <string name="app_used_data_B">B</string>

    <!-- 本地日志,无需翻译-->
    <string name="init_state" translatable="false">初始化</string>
    <string name="language_time_setting" translatable="false">语言时区配置</string>
    <string name="device_inspect" translatable="false">设备自检</string>
    <string name="configure_state" translatable="false">配置网络</string>
    <string name="checking_state" translatable="false">绑定检查</string>
    <string name="binding_state" translatable="false">绑定中</string>
    <string name="report_os_type_state" translatable="false">切换系统中</string>
    <string name="working_state" translatable="false">获取服务端主语言</string>
    <string name="error_state" translatable="false">异常</string>
    <string name="settings_scenario_list" translatable="false">设置方案列表</string>
    <string name="upload_settings_scenario" translatable="false">上传设置方案</string>
    <string name="settings_scenario_info" translatable="false">更新设置方案</string>
    <string name="inspect_err_title_en" tools:ignore="MissingTranslation">Sorry, the robot is experiencing technical difficulties \nPlease take a photo and send to our technical personnel</string>
    <string name="inspect_err_feedback_tips_en">Once you send the feedback,\n please restart manually to restore the robot</string>
    <!--START 非中文环境需翻译,中文下无需翻译 -->
    <string name="robot_name_zh" translatable="false">小豹%1$s号</string>
    <string name="robot_name_en" translatable="false">Robot %1$s</string>
    <!-- END 非中文环境需翻译,中文下无需翻译-->
</resources>