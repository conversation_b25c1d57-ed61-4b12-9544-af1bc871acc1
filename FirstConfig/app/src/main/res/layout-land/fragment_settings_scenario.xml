<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data class="SettingsScenarioBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:text="@string/settings_scenario_title"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="@string/settings_scenario_subtitle"
                android:textColor="#D8D8D8"
                android:textSize="10sp" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ListView
                    android:id="@+id/content_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingTop="5dp"
                    android:divider="#00000000"
                    android:dividerHeight="3dp"
                    android:paddingStart="91dp"
                    android:layout_marginBottom="80dp"
                    android:paddingEnd="91dp" />

                <LinearLayout
                    android:id="@+id/failed_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/settings_scenario_img"
                        android:layout_width="140dp"
                        android:layout_height="116dp"
                        android:layout_marginTop="5dp" />

                    <TextView
                        android:id="@+id/failed_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10sp"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/confirm_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_gravity="bottom|center"
                    android:layout_marginBottom="30dp"
                    android:visibility="gone">

                    <Button
                        android:id="@+id/navigation_back"
                        android:layout_width="150dp"
                        android:layout_height="match_parent"
                        android:background="@drawable/back_btn_bg"
                        android:onClick="@{clickListener}"
                        android:text="@string/back"
                        android:textColor="@color/white"
                        android:textSize="13sp" />

                    <Button
                        android:id="@+id/navigation_confirm"
                        android:layout_width="150dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="26dp"
                        android:background="@drawable/confirm_btn_bg"
                        android:onClick="@{clickListener}"
                        android:text="@string/finish_active"
                        android:textColor="@color/white"
                        android:textSize="13sp" />
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/loading_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <ProgressBar
                android:id="@+id/loading_progress"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_marginTop="116dp"
                android:indeterminateDrawable="@drawable/icon_loading" />

            <TextView
                android:id="@+id/loading_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="46dp"
                android:gravity="center"
                android:text="@string/loading"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </LinearLayout>
    </FrameLayout>
</layout>