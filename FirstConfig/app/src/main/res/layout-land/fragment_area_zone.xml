<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="AreaZoneBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            android:id="@+id/tv_title"
            style="@style/TitleStyle"
            android:text="@string/area_zone_select"/>

        <ListView
            android:id="@+id/lv_area_zone"
            android:layout_width="match_parent"
            android:layout_height="520px"
            android:layout_above="@+id/rel_butt"
            android:layout_below="@+id/tv_title"
            android:layout_marginTop="10dp"
            android:layout_marginStart="140px"
            android:layout_marginEnd="140px"
            android:divider="@color/white_15"
            android:dividerHeight="1px"
            android:scrollbars="none" />

        <com.ainirobot.firstconfig.ui.view.SideBar
            android:id="@+id/sideBar"
            android:layout_width="16dp"
            android:layout_height="match_parent"
            android:layout_above="@+id/rel_butt"
            android:layout_below="@+id/tv_title"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="-3dp"
            android:layout_marginBottom="-6dp"
            android:layout_marginEnd="6dp"
            />

        <LinearLayout
            android:id="@+id/rel_butt"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="108px"
            android:layout_width="wrap_content"
            android:layout_height="108px"
            android:layout_centerHorizontal="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/butt_back"
                style="@style/ButtonStyle"
                android:layout_width="450px"
                android:layout_height="108px"
                android:layout_marginEnd="120px"
                android:background="@drawable/cancel_btn_bg"
                android:text="@string/back"
                android:onClick="@{clickListener}"/>

            <Button
                android:id="@+id/butt_next"
                style="@style/ButtonStyle"
                android:layout_width="450px"
                android:layout_height="108px"
                android:text="@string/next1"
                android:onClick="@{clickListener}"/>
        </LinearLayout>

    </RelativeLayout>
</layout>
