<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data class="LanguageBinding">
        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            style="@style/TitleStyle"
            android:id="@+id/title"
            android:text="@string/china_choose_language"
            />

        <ListView
            android:id="@+id/lv_language"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/title"
            android:layout_marginStart="140px"
            android:layout_marginTop="60px"
            android:layout_marginEnd="140px"
            android:divider="@color/white_15"
            android:dividerHeight="1px"
            android:scrollbars="none">

        </ListView>

        <Button
            style="@style/ButtonStyle"
            android:id="@+id/butt_next"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:text="@string/next"
            android:textSize="40px"
            android:onClick="@{clickListener}"/>


    </RelativeLayout>
</layout>
