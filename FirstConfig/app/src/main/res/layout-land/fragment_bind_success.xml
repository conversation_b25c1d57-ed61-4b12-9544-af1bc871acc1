<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="BindSuccessBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <RelativeLayout
            android:id="@+id/rl_bind"
            android:layout_width="305px"
            android:layout_height="305px"
            android:layout_marginTop="173px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="305px"
                android:layout_height="305px"
                android:contentDescription="@string/app_name"
                android:scaleType="fitCenter"
                android:src="@drawable/mini_robot" />

            <ImageView
                android:layout_width="100px"
                android:layout_height="100px"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:contentDescription="@string/app_name"
                android:scaleType="fitCenter"
                android:src="@drawable/first_success_img" />
        </RelativeLayout>


        <TextView
            android:id="@+id/bind_success_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="57px"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/bind_success_title"
            android:textColor="@color/white_ff"
            android:textSize="60px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rl_bind" />

        <TextView
            android:id="@+id/bing_success_robot_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30px"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/white_ff"
            android:textSize="40px"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bind_success_title" />

        <TextView
            android:id="@+id/bottomBtn"
            style="@style/ButtonStyle"
            android:layout_marginBottom="29px"
            android:text="@string/bind_success_create_map"
            app:layout_constraintBottom_toTopOf="@id/bottomBtnDes"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:onClick="@{clickListener}"/>

        <TextView
            android:id="@+id/bottomBtnDes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="84px"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/bind_success_title_des"
            android:textColor="@color/confirm_btn_bg"
            android:textSize="40px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:onClick="@{clickListener}"/>

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:includeFontPadding="true"
            android:textSize="@dimen/font_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintGuide_percent="0.803"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintGuide_percent="0.771"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>