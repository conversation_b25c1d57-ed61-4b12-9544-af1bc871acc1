<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="144px"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_language"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            android:textColor="#ffffff"
            android:textSize="40px"/>

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/im_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_gravity="center_vertical"
            android:src="@drawable/unselect"/>
    </LinearLayout>
</LinearLayout>
