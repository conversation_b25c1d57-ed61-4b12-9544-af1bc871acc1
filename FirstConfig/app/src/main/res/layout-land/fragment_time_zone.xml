<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="TimeZoneBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            android:id="@+id/tv_title"
            style="@style/TitleStyle"
            android:text="@string/time_zone_select" />

        <TextView
            android:id="@+id/nextGmt"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:padding="8dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="36dp"
            android:layout_marginEnd="40dp"
            android:onClick="@{clickListener}"
            android:background="@drawable/next_time_zone"/>

        <TextView
            android:id="@+id/preGmt"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:padding="8dp"
            android:layout_toStartOf="@+id/nextGmt"
            android:layout_marginTop="36dp"
            android:layout_marginEnd="30dp"
            android:onClick="@{clickListener}"
            android:background="@drawable/pre_time_zone"/>

        <ListView
            android:layout_below="@id/tv_title"
            android:layout_marginTop="10dp"
            android:id="@+id/lv_time_zone"
            android:layout_width="match_parent"
            android:layout_height="520px"
            android:layout_marginStart="140px"
            android:layout_marginEnd="140px"
            android:divider="@color/white_15"
            android:dividerHeight="1px"
            android:scrollbars="none" />


        <LinearLayout
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="108px"
            android:layout_width="wrap_content"
            android:layout_height="108px"
            android:layout_centerHorizontal="true"
            android:orientation="horizontal">

            <Button
                android:id="@+id/butt_back"
                style="@style/ButtonStyle"
                android:layout_width="450px"
                android:layout_height="108px"
                android:layout_marginEnd="120px"
                android:background="@drawable/cancel_btn_bg"
                android:text="@string/back"
                android:onClick="@{clickListener}"/>

            <Button
                android:id="@+id/butt_next"
                style="@style/ButtonStyle"
                android:layout_width="450px"
                android:layout_height="108px"
                android:text="@string/next1"
                android:onClick="@{clickListener}"/>
        </LinearLayout>

    </RelativeLayout>

</layout>
