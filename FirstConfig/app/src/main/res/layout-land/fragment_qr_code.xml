<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="QrCodeBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            android:id="@+id/title"
            style="@style/TitleStyle"
            android:text="@string/wx_qr_code_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/title_leftback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitCenter"
            android:src="@drawable/img_back_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="12dp"
            android:layout_marginTop="32dp"
            android:visibility="visible"
            android:padding="3dp"
            android:onClick="@{clickListener}"/>


        <ImageView
            android:id="@+id/qr_code"
            android:layout_width="386px"
            android:layout_height="386px"
            android:layout_marginTop="84px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <TextView
            android:id="@+id/qr_code_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="47px"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/wx_qr_code_loading"
            android:textColor="@color/white_ff"
            android:textSize="40px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/qr_code" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="46dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/centerFlag"
                android:layout_width="1px"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"/>

            <LinearLayout
                android:id="@+id/sn_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@+id/centerFlag"
                android:layout_marginEnd="2dp"
                android:layout_centerVertical="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@string/sn_number_for_pc"
                    android:textColor="@color/white_50"
                    android:textSize="@dimen/font_12" />

                <TextView
                    android:id="@+id/sn_number_tx"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:letterSpacing="0.1"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_14" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/bind_code_pc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@+id/centerFlag"
                android:layout_centerVertical="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:singleLine="true"
                    android:text="@string/bind_code_for_pc"
                    android:textColor="@color/white_50"
                    android:textSize="@dimen/font_12" />

                <TextView
                    android:id="@+id/bind_code_tx"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:letterSpacing="0.1"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_14" />

            </LinearLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_loading"
            android:layout_width="386px"
            android:layout_height="386px"
            android:layout_marginTop="84px"
            android:background="@color/white_1A"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">

            <ImageView
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_centerInParent="true"
                android:src="@drawable/first_qrcode_loading" />
        </RelativeLayout>


        <TextView
            android:id="@+id/qr_code_load_retry"
            android:layout_width="640px"
            android:layout_height="108px"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="32dp"
            android:layout_marginBottom="122px"
            android:background="@drawable/selector_locate_failure_btn"
            android:gravity="center"
            android:includeFontPadding="true"
            android:onClick="@{clickListener}"
            android:padding="6dp"
            android:textColor="@color/white_ff"
            android:textSize="@dimen/font_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:includeFontPadding="true"
            android:onClick="ignoreBind"
            android:textSize="@dimen/font_14"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>