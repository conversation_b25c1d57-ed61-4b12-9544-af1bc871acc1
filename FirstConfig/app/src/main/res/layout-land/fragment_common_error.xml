<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="InnerErrorBinding">
        <import type="com.ainirobot.firstconfig.utils.SystemUtils"/>
        <import type="android.view.View"/>
    </data>

    <LinearLayout
        android:id="@+id/inspection_error_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:orientation="vertical"
        android:padding="20dp"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="160px"
                android:layout_height="160px"
                android:layout_marginTop="5dp"
                android:src="@drawable/inspect_error_img" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/inspect_error_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:lineSpacingExtra="10px"
                    android:text="@string/inspect_err_title"
                    android:textColor="@color/color_ff713c"
                    android:textSize="35px" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/inspect_error_shutdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/custom_background"
            android:orientation="horizontal"
            android:padding="10dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:gravity="center_vertical"
                android:src="@drawable/battery_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="center_vertical"
                android:text="@string/inspect_err_btn_shutdown"
                android:textColor="@color/white"
                android:textSize="15sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_id_sn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:lineSpacingExtra="16px"
            android:text="@string/inspect_err_navigation_log_id"
            android:textColor="@color/white"
            android:textSize="55px" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:text="@string/inspect_err_error"
                    android:textColor="@color/white"
                    android:textSize="40px" />

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="4dp"
                    android:scrollbars="vertical">

                    <TextView
                        android:id="@+id/inspect_error_message"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:lineSpacingExtra="10px"
                        android:textColor="@color/white"
                        android:textSize="40px" />
                </ScrollView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="300dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_qr_code"
                    android:layout_width="148px"
                    android:layout_height="148px"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/first_qrcode_logo"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_feedback_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:lineSpacingExtra="10px"
                        android:text="@string/inspect_err_feedback_tips"
                        android:textColor="@color/white"
                        android:textSize="36px" />


                    <TextView
                        android:id="@+id/tv_qr_code_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:lineSpacingExtra="10px"
                        android:text="@string/inspect_err_qr_code_tips"
                        android:textColor="@color/white"
                        android:textSize="40px"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>


        <ImageView
            android:layout_width="match_parent"
            android:layout_height="80px"
            android:src="@drawable/check_bg_img"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/lxc_repair"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/tv_phone_number">

            <Button
                android:id="@+id/butt_lxc_repair"
                android:layout_width="140dp"
                android:layout_height="30dp"
                android:padding="5dp"
                android:layout_centerHorizontal="true"
                android:background="@drawable/confirm_btn_bg"
                android:text="@string/lxc_repair"
                android:textColor="#ffffff"
                android:textSize="40px" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="@{SystemUtils.isLanguageOverseas ? View.GONE : View.VISIBLE}">

            <ImageView
                android:layout_width="42px"
                android:layout_height="42px"
                android:layout_marginEnd="5dp"
                android:src="@drawable/inspect_error_phone_img"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/inspect_err_after_sales_number"
                android:textColor="@color/white"
                android:textSize="40px"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>

    </LinearLayout>

</layout>


