<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/toast_background"
        android:textColor="#ffffff"
        android:gravity="center"
        android:paddingTop="27px"
        android:paddingBottom="27px"
        android:paddingEnd="70px"
        android:paddingStart="70px"
        android:textSize="40px"/>

</androidx.constraintlayout.widget.ConstraintLayout>