<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_bg">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:text="@string/woowa_title_tips"
        android:textColor="@color/dialog_title_title_text"
        android:textSize="15sp"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/enable_one_time_password"
        android:textColor="@color/dialog_title_text_color"
        android:textSize="@dimen/font_10"
        android:gravity="center"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />


    <TextView
        android:id="@+id/confirm"
        android:layout_width="70dp"
        android:layout_height="26dp"
        android:background="@drawable/shape_btn_round_corner"
        android:ellipsize="marquee"
        android:gravity="center"
        android:fontFamily="sans-serif-medium"
        android:text="@string/got_it"
        android:textColor="@color/white"
        android:textSize="@dimen/font_11"
        android:layout_marginBottom="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>



</androidx.constraintlayout.widget.ConstraintLayout>
