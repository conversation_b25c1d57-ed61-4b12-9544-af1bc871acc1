<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data class="SettingsScenarioBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30sp"
                android:layout_marginTop="33dp"
                android:text="@string/settings_scenario_title"
                android:textColor="@color/white"
                android:textSize="25sp" />

            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="33sp"
                android:layout_marginTop="5dp"
                android:text="@string/settings_scenario_subtitle"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <ListView
                android:id="@+id/content_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="90dp"
                android:divider="#00000000"
                android:dividerHeight="3dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/failed_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:id="@+id/failed_line"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_centerVertical="true" />

            <ImageView
                android:id="@+id/settings_scenario_img"
                android:layout_width="144dp"
                android:layout_height="120dp"
                android:layout_above="@id/failed_title"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="-10dp" />

            <TextView
                android:id="@+id/failed_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/failed_line"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="14dp"
                android:gravity="center"
                android:textColor="@color/white" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/confirm_layout"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_gravity="bottom|center"
            android:layout_marginBottom="33dp"
            android:visibility="gone">

            <Button
                android:id="@+id/navigation_back"
                android:layout_width="113dp"
                android:layout_height="match_parent"
                android:background="@drawable/back_btn_bg"
                android:onClick="@{clickListener}"
                android:text="@string/back"
                android:textColor="@color/white"
                android:textSize="13sp" />

            <Button
                android:id="@+id/navigation_confirm"
                android:layout_width="113dp"
                android:layout_height="match_parent"
                android:layout_marginStart="26dp"
                android:background="@drawable/confirm_btn_bg"
                android:onClick="@{clickListener}"
                android:text="@string/finish_active"
                android:textColor="@color/white"
                android:textSize="13sp" />
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/loading_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <View
                android:id="@+id/loading_line"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_centerVertical="true" />

            <ProgressBar
                android:id="@+id/loading_progress"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_above="@id/loading_title"
                android:layout_centerHorizontal="true"
                android:indeterminateDrawable="@drawable/icon_loading" />

            <TextView
                android:id="@+id/loading_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/loading_line"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="54dp"
                android:gravity="center"
                android:text="@string/loading"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </RelativeLayout>
    </FrameLayout>
</layout>