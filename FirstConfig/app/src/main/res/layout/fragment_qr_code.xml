<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="QrCodeBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="31dp"
            android:text="@string/wx_qr_code_title"
            android:textColor="@color/white_ff"
            android:textSize="@dimen/font_28"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/gl_title_top" />

        <ImageView
            android:id="@+id/title_leftback"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="fitCenter"
            android:background="@drawable/img_back_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="0dp"
            android:layout_marginTop="31dp"
            android:visibility="visible"
            android:padding="3dp"
            android:onClick="@{clickListener}"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="85dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toTopOf="@+id/qr_code"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:id="@+id/sn_number"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="131dp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@string/sn_number_for_pc"
                    android:textColor="@color/white_AF"
                    android:textSize="@dimen/font_11" />

                <TextView
                    android:id="@+id/sn_number_tx"
                    android:layout_width="85dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/first_set_line_img"
                    android:gravity="center"
                    android:letterSpacing="0.1"
                    android:singleLine="true"
                    android:textColor="@color/white_AF"
                    android:textSize="@dimen/font_14" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/bind_code_pc"
                android:layout_width="90dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginTop="131dp"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@string/bind_code_for_pc"
                    android:textColor="@color/white_AF"
                    android:textSize="@dimen/font_11" />

                <TextView
                    android:id="@+id/bind_code_tx"
                    android:layout_width="85dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/first_set_line_img"
                    android:gravity="center"
                    android:letterSpacing="0.1"
                    android:singleLine="true"
                    android:textColor="@color/white_AF"
                    android:textSize="@dimen/font_14" />

            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/qr_code"
            android:layout_width="192dp"
            android:layout_height="192dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:id="@+id/rl_loading"
            android:layout_width="192dp"
            android:layout_height="192dp"
            android:background="@color/white_1A"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_centerInParent="true"
                android:src="@drawable/first_qrcode_loading" />
        </RelativeLayout>

        <TextView
            android:id="@+id/qr_code_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/wx_qr_code_loading"
            android:textColor="@color/white_ff"
            android:textSize="@dimen/font_11"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gl_des_top" />

        <TextView
            android:id="@+id/qr_code_load_retry"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="32dp"
            android:background="@drawable/selector_locate_failure_btn"
            android:gravity="center"
            android:includeFontPadding="true"
            android:onClick="@{clickListener}"
            android:padding="6dp"
            android:textColor="@color/white_ff"
            android:textSize="@dimen/font_12"
            app:layout_constraintTop_toBottomOf="@+id/gl_bottom" />

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:includeFontPadding="true"
            android:onClick="ignoreBind"
            android:textSize="@dimen/font_14"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_title_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.057"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_des_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.714"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.796"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>