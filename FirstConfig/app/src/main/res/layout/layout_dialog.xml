<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_bg">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="@string/cloud_server_select"
        android:textColor="@color/black"
        android:textSize="15sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/disconnect_dialog_content"
        android:textColor="@color/dialog_title_text_color"
        android:textSize="13sp"
        android:gravity="center"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title" />
    
    <ListView
        android:id="@+id/lv_servers"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintBottom_toTopOf="@+id/view_line"/>


    <View
        android:id="@+id/view_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="83px"
        android:background="@color/dialog_divide_line"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/ll_buttons" />


    <LinearLayout
        android:id="@+id/ll_buttons"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        >
        <TextView
            android:id="@+id/cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="1px"
            android:background="@drawable/selector_dialog_btn_bg"
            android:gravity="center"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:text="@string/cloud_select_cancel"
            android:textColor="@color/dialog_title_title_text"
            android:textSize="@dimen/font_12"
            android:visibility="gone"
            />

        <View
            android:id="@+id/vertical_view_line"
            android:layout_width="1px"
            android:layout_height="match_parent"
            android:background="@color/vertical_line"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/cancel"
            app:layout_constraintEnd_toStartOf="@+id/confirm"
            app:layout_constraintTop_toTopOf="@+id/view_line" />


        <TextView
            android:id="@+id/confirm"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selector_dialog_btn_bg"
            android:gravity="center"
            android:fontFamily="sans-serif-medium"
            android:text="@string/cloud_select_ok"
            android:textColor="@color/dialog_title_title_text"
            android:textSize="@dimen/font_14" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
