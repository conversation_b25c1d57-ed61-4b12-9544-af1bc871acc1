<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="TimeZoneBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="110px"
            android:layout_marginTop="110px"
            android:text="@string/time_zone_select"
            android:textColor="#ffffff"
            android:textSize="80px" />

        <TextView
            android:id="@+id/nextGmt"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:padding="8dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="110px"
            android:layout_marginEnd="16dp"
            android:onClick="@{clickListener}"
            android:background="@drawable/next_time_zone"/>

        <TextView
            android:id="@+id/preGmt"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:padding="8dp"
            android:layout_toStartOf="@+id/nextGmt"
            android:layout_marginTop="110px"
            android:layout_marginEnd="20dp"
            android:onClick="@{clickListener}"
            android:background="@drawable/pre_time_zone"/>

        <ListView
            android:id="@+id/lv_time_zone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/rel_butt"
            android:layout_below="@+id/tv_title"
            android:layout_marginStart="110px"
            android:layout_marginTop="110px"
            android:layout_marginEnd="110px"
            android:divider="@null"
            android:scrollbars="none" />

        <RelativeLayout
            android:id="@+id/rel_butt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="162px"
            android:layout_marginBottom="140px"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/butt_back"
                    android:layout_width="340px"
                    android:layout_height="120px"
                    android:layout_marginEnd="100px"
                    android:background="@drawable/back_btn_bg"
                    android:text="@string/back"
                    android:textColor="#ffffff"
                    android:textSize="40px"
                    android:onClick="@{clickListener}"/>

                <Button
                    android:id="@+id/butt_next"
                    android:layout_width="340px"
                    android:layout_height="120px"
                    android:background="@drawable/confirm_btn_bg"
                    android:text="@string/next1"
                    android:textColor="#ffffff"
                    android:textSize="40px"
                    android:onClick="@{clickListener}"/>
            </LinearLayout>


        </RelativeLayout>

    </RelativeLayout>
</layout>
