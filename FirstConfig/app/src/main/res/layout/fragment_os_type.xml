<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="SetOSTypeBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="110px"
            android:layout_marginTop="110px"
            android:text="@string/os_type_select"
            android:textColor="#ffffff"
            android:textSize="80px" />

        <ListView
            android:id="@+id/lv_types"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/rel_butt"
            android:layout_below="@+id/tv_title"
            android:layout_marginStart="110px"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="110px"
            android:divider="@null"
            android:scrollbars="none" />


        <RelativeLayout
            android:id="@+id/rel_butt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/tv_hint"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="15dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/butt_back"
                    android:layout_width="340px"
                    android:layout_height="120px"
                    android:layout_marginEnd="100px"
                    android:background="@drawable/back_btn_bg"
                    android:onClick="@{clickListener}"
                    android:text="@string/back"
                    android:textColor="#ffffff"
                    android:textSize="40px" />

                <Button
                    android:id="@+id/butt_next"
                    android:layout_width="340px"
                    android:layout_height="120px"
                    android:background="@drawable/confirm_btn_bg"
                    android:onClick="@{clickListener}"
                    android:text="@string/next1"
                    android:textColor="#ffffff"
                    android:textSize="40px" />
            </LinearLayout>


        </RelativeLayout>


        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="110px"
            android:layout_marginEnd="110px"
            android:layout_marginBottom="10dp"
            android:alpha="0.5"
            android:gravity="center"
            android:text=""
            android:textColor="@color/white"
            android:textSize="@dimen/font_10" />

    </RelativeLayout>
</layout>
