<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="SelfInspectBinding"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/inspecting_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/inspecting_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitCenter"
            android:src="@drawable/inspect_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/gl_top" />

        <ImageView
            android:id="@+id/inspecting_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="73px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/inspecting_image" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="73px"
            android:alpha="0.5"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/self_inspection_speech"
            android:textColor="@color/white_ff"
            android:textSize="@dimen/font_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/inspecting_loading" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_top"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.375"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!--    &lt;!&ndash;200px * 200px&ndash;&gt;-->
        <!--    <android.support.constraint.ConstraintLayout-->
        <!--        android:layout_width="match_parent"-->
        <!--        android:layout_height="match_parent"-->
        <!--        android:background="@color/inspecting_bg"-->
        <!--        android:visibility="visible"-->
        <!--        app:layout_constraintBottom_toBottomOf="parent"-->
        <!--        app:layout_constraintStart_toStartOf="parent"-->
        <!--        app:layout_constraintEnd_toEndOf="parent"-->
        <!--        app:layout_constraintTop_toTopOf="parent">-->

        <!--        -->
        <!--    </android.support.constraint.ConstraintLayout>-->

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>