<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/settings_scenario_item"
    android:orientation="horizontal"
    android:paddingStart="17dp"
    android:paddingEnd="17dp">

    <TextView
        android:id="@+id/tv_scenario"
        android:layout_width="wrap_content"
        android:layout_height="43dp"
        android:layout_gravity="center_vertical"
        android:layout_weight="1.0"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="13sp" />

    <ImageView
        android:id="@+id/im_select"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:src="@drawable/unselect" />
</LinearLayout>
