<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="LoginErrorBinding"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_ff"
        >

        <TextView
            android:id="@+id/login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gl_top"
            android:text="@string/login_error_title"
            android:textSize="@dimen/font_28"
            android:textColor="@color/inspect_error"
            android:gravity="center"
            android:singleLine="true"
            android:ellipsize="end"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/login_title"
            android:layout_marginTop="4dp"
            android:text="@string/login_error_title_des"
            android:textSize="@dimen/font_28"
            android:textColor="@color/inspect_error"
            android:gravity="center"
            android:singleLine="true"
            android:ellipsize="end"/>

        <ImageView
            android:id="@+id/centerImage"
            android:layout_width="78dp"
            android:layout_height="78dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/inspect_error_img"
            android:scaleType="fitXY"/>

        <TextView
            android:id="@+id/login_error_msg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/centerImage"
            android:layout_marginTop="28dp"
            android:textSize="@dimen/font_12"
            android:textColor="@color/white_ff"
            android:gravity="center"
            android:singleLine="true"
            android:ellipsize="marquee"/>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/gl_phone_number"
            android:layout_marginTop="28dp"
            android:orientation="horizontal"
            android:gravity="center">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/inspect_error_phone"
                android:scaleType="fitXY"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:text="@string/self_inspection_service_number"
                android:textSize="@dimen/font_12"
                android:textColor="@color/white_ff"
                android:singleLine="true"
                android:ellipsize="marquee"/>
        </LinearLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.104"/>


        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_phone_number"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.806"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>