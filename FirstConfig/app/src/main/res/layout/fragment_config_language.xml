<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data class="LanguageBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>



    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/first_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="28dp"
            android:text="@string/china_choose_language"
            android:textColor="#ffffff"
            android:textSize="17sp" />


        <ListView
            android:id="@+id/lv_language"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/title"
            android:layout_above="@+id/rel_butt"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="36dp"
            android:divider="@color/white_15"
            android:dividerHeight="1px"
            android:scrollbars="none">

        </ListView>

        <RelativeLayout
            android:id="@+id/rel_butt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="18dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/butt_next"
                android:layout_width="130dp"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:layout_centerHorizontal="true"
                android:background="@drawable/language_btn_bg"
                android:text="@string/next"
                android:textAllCaps="false"
                android:textColor="#ffffff"
                android:textSize="@dimen/font_11"
                android:onClick="@{clickListener}"/>

        </RelativeLayout>
    </RelativeLayout>
</layout>
