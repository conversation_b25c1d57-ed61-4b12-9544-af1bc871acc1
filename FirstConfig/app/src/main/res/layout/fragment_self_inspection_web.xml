<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data class="SelfInspectWebBinding">

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/inspecting_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/inspect_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:alpha="0.5"
            android:ellipsize="end"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/self_inspection_speech"
            android:textColor="@color/white_ff"
            android:textSize="@dimen/font_10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/inspecting_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="5dp"
            app:layout_constraintEnd_toStartOf="@+id/inspect_title"
            app:layout_constraintTop_toTopOf="parent" />

        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="50dp"
            android:layout_marginBottom="50dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/butt_next"
            android:layout_width="130dp"
            android:layout_height="36dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/inspect_web_btn_next_selector"
            android:enabled="false"
            android:onClick="@{clickListener}"
            android:text="@string/next"
            android:textAllCaps="false"
            android:textColor="@drawable/inspect_web_text_next_selector"
            android:textSize="@dimen/font_11"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>