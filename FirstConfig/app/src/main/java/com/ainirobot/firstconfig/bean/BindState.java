package com.ainirobot.firstconfig.bean;

import com.ainirobot.coreservice.client.Definition;
import com.google.gson.annotations.SerializedName;

public class BindState {
    @SerializedName(Definition.BIND_STATUS)
    private int mBindState;
    @SerializedName(Definition.ROBOT_NAME)
    private String mRobotName;

    public int getBindState() {
        return mBindState;
    }

    public String getRobotName() {
        return mRobotName;
    }

    @Override
    public String toString() {
        return "BindState{" +
                "mBindState=" + mBindState +
                ", mRobotName='" + mRobotName + '\'' +
                '}';
    }
}
