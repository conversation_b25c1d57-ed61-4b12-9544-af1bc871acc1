/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.utils;

import android.app.AppOpsManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigApplication;

import androidx.annotation.RequiresApi;


public class ManifestUtil {
    private static final String TAG = ManifestUtil.class.getSimpleName();

    public static final String M_PERMISSION_LOCATION = "android.permission.ACCESS_COARSE_LOCATION";
    public static final String M_PERMISSION_ALERT = "android.permission.SYSTEM_ALERT_WINDOW";
    public static final String M_PERMISSION_USAGE_STATS = "android.permission.PACKAGE_USAGE_STATS";
    public static final String M_PERMISSION_READ_PHONE_STATE = "android.permission.READ_PHONE_STATE";

    /**
     * @param context
     * @return
     */
    public static String getPackageName(Context context) {
        PackageInfo pkg = null;
        try {
            synchronized (ManifestUtil.class) {//  如果一个process中使用的Binder内容超过了1M; 会发生Package manager has died. 避免多个线程同时调用.
                pkg = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            }
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }

        return (pkg == null) ? null : pkg.applicationInfo.loadLabel(context.getPackageManager()).toString();
    }

    /**
     * get AndroidManifest.xml  metaData CHANNEL_ID
     *
     * @param context
     * @return
     */
    public static int getChannelID(Context context) {
        if (context == null) return 0;
        ApplicationInfo info;

        int channelId = 0;
        try {
            info = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
            channelId = info.metaData.getInt("CHANNEL_ID");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return channelId;
    }


    /**
     * @param context
     * @return
     */
    public static String getVersionName(Context context) {
        if (context == null) return "";
        PackageInfo pkg = null;
        try {
            pkg = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return (pkg == null) ? null : pkg.versionName;
    }

    /**
     * @param context
     * @return
     */
    public static int getVersionCode(Context context) {
        if (context == null) return 0;
        PackageInfo pkg = null;
        try {
            pkg = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
        } catch (NameNotFoundException e) {
            e.printStackTrace();
        }
        return (pkg == null) ? 0 : pkg.versionCode;
    }


    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static int getUsageStatsOpsMode() {
        final AppOpsManager appOpsManager = (AppOpsManager) FirstConfigApplication.getAppContext().getSystemService(Context.APP_OPS_SERVICE);
        if (appOpsManager != null) {
            int mode = appOpsManager.checkOpNoThrow(AppOpsManager.OPSTR_GET_USAGE_STATS, Process.myUid(),
                    FirstConfigApplication.getAppContext().getPackageName());
            Log.d(TAG, "usage:checkOpNoThrow  mode = " + mode);
            return mode;
        } else {
            return -1;
        }
    }

    public static void startWatchingPermission(final Context context, final Class jumbToActivity) {
        final AppOpsManager appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            appOpsManager.startWatchingMode(AppOpsManager.OPSTR_GET_USAGE_STATS, context.getPackageName(), new AppOpsManager.OnOpChangedListener() {
                @Override
                public void onOpChanged(String s, String s1) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        int mode = appOpsManager.checkOpNoThrow(AppOpsManager.OPSTR_GET_USAGE_STATS, Process.myUid(), context.getPackageName());
                        if (mode == AppOpsManager.MODE_ALLOWED) {
                            appOpsManager.stopWatchingMode(this);
                            Intent intent = new Intent(context, jumbToActivity);
                            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                            context.startActivity(intent);
//                            ConfigSetting.getIns(context).setJumpToSettingFlag(false);
                        }

                    }

                }
            });
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void startWatchingPermission(final Context context, final String permission, final Class jumbToActivity) {
        final AppOpsManager appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        appOpsManager.startWatchingMode(permission, context.getPackageName(), new AppOpsManager.OnOpChangedListener() {
            @Override
            public void onOpChanged(String s, String s1) {
                int mode = appOpsManager.checkOpNoThrow(permission, Process.myUid(), context.getPackageName());
                if (mode == AppOpsManager.MODE_ALLOWED) {
                    appOpsManager.stopWatchingMode(this);
                    Log.d(TAG, "monitor:startWatchingPermission onOpChanged MODE_ALLOWED");
                    Intent intent = new Intent(context, jumbToActivity);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    context.startActivity(intent);
                }
            }
        });
    }
}
