package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.OSTypeBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.utils.CloudUtils;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置系统类型
 */
public class SetOSTypeViewModel extends BaseViewModel<BaseModel> {

    private final List<OSTypeBean> mList = new ArrayList<>();
    private final MutableLiveData<List<OSTypeBean>> state = new MutableLiveData<>();
    private String mOSType;

    public SetOSTypeViewModel(@NonNull Application application) {
        super(application);
    }

    public MutableLiveData<List<OSTypeBean>> getState() {
        return state;
    }

    @Override
    public void onStart() {
        super.onStart();

        initOSType();
    }

    private void initOSType() {
        mOSType = SharedPrefUtil.getInstance().getOSType();
        if (TextUtils.isEmpty(mOSType)) {
            mOSType = Definition.OSType.DEFAULT.getValue();
        }
        Log.d(TAG, "initOSType last localServerCode: " + mOSType);
        mList.addAll(CloudUtils.getServerType(mOSType));
        Log.d(TAG, "initOSType last mList: " + mList.size());
        state.postValue(mList);
    }

    public void updateOSType(String osType) {
        if (TextUtils.isEmpty(osType)) {
            Log.e(TAG, "updateOSType failed , osType is empty !");
            return;
        }
        mOSType = osType;
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.butt_back:
                EventBus.getDefault().post(new UIMessage<>(FirstConfigDef.OPEN_FRAGMENT_SET_LANGUAGE),
                        FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                break;
            case R.id.butt_next:
                next();
                break;
            default:
                break;
        }
    }

    private void next() {
        Log.e(TAG, "next" + ProductInfo.isOverSea() + ", mOSType:" + mOSType);
        SharedPrefUtil.getInstance().setOSType(mOSType);
        EventBus.getDefault().post(new UIMessage<>(ProductInfo.isOverSea() ?
                        FirstConfigDef.OPEN_FRAGMENT_CLOUD_SERVER : FirstConfigDef.OPEN_FRAGMENT_SET_TIME_ZONE),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

}
