/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.utils;

import android.content.Context;
import android.util.DisplayMetrics;
import android.util.Log;


import com.ainirobot.coreservice.client.RobotSettings;

import java.lang.reflect.Field;

public class DimensionUtils {
    private static final String TAG = DimensionUtils.class.getSimpleName();

    private static float DENSITY;
    private static int DENSITY_DPI;
    private static int WIDTH_PIXELS;
    private static float WIDTH_DP;
    private static int HEIGHT_PIXELS;
    private static float HEIGHT_DP;
    private static int PRODUCT_STATUS_BAR_MARGIN;
    private static int STATUS_BAR_HEIGHT;
    private static float SCALED_DENSITY;


    public static int dpToPx(float dp) {
        return (int) (DENSITY * dp + 0.5);
    }

    public static float pxToDp(int px) {
        return px / DENSITY;
    }

    /**
     * 将px值转换为sp值，保证文字大小不变
     *
     * @param pxValue （DisplayMetrics类中属性scaledDensity）
     * @return
     */
    public static int px2sp(Context context, float pxValue) {
        return (int) (pxValue / SCALED_DENSITY + 0.5f);
    }

    /**
     * 将sp值转换为px值，保证文字大小不变
     *
     * @param spValue （DisplayMetrics类中属性scaledDensity）
     * @return
     */
    public static int sp2px(float spValue) {
        return (int) (spValue * SCALED_DENSITY + 0.5f);
    }

    /**
     * do it when app start up
     *
     * @param context
     */
    public static void initDimension(Context context) {
        if (context == null || context.getResources() == null) {
            Log.d(TAG, "initDimension return ");
            return;
        }
        try {
            DisplayMetrics dm = context.getResources().getDisplayMetrics();
            DENSITY = dm.density;
            DENSITY_DPI = dm.densityDpi;
            WIDTH_PIXELS = dm.widthPixels;
            HEIGHT_PIXELS = dm.heightPixels;
            SCALED_DENSITY = dm.scaledDensity;
            WIDTH_DP = pxToDp(WIDTH_PIXELS);
            HEIGHT_DP = pxToDp(HEIGHT_PIXELS);
            STATUS_BAR_HEIGHT = getStatusBarHeight(context);
            PRODUCT_STATUS_BAR_MARGIN = 0;
            Log.d(TAG, "WIDTH_PIXELS = " + WIDTH_PIXELS + "; HEIGHT_PIXELS = " + HEIGHT_PIXELS + ",WIDTH_DP = " + WIDTH_DP + ",HEIGHT_DP = " + HEIGHT_DP + ",DENSITY = " + DENSITY + ",SN = " + RobotSettings.getSystemSn());
        } catch (Exception e) {
            e.printStackTrace();
            Log.d(TAG, "initDimension  error = " + e.getLocalizedMessage());
        }
    }

    // 获取手机状态栏高度
    public static int getStatusBarHeight(Context context) {
        Class<?> c = null;
        Object obj = null;
        Field field = null;
        int x = 0, statusBarHeight = 0;
        try {
            c = Class.forName("com.android.internal.R$dimen");
            obj = c.newInstance();
            field = c.getField("status_bar_height");
            x = Integer.parseInt(field.get(obj).toString());
            statusBarHeight = context.getResources().getDimensionPixelSize(x);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return statusBarHeight;
    }

    /**
     * 多分辨率的自适应，根据360dp的UI标准自动转换成对应实际手机dp分辨率下的像素值
     */
    public static float dpSuit2Float(int dp) {
        return dp * WIDTH_PIXELS / DENSITY / 360.0f * DENSITY + 0.5f;
    }

    /**
     * 多分辨率的自适应，根据360dp的UI标准自动转换成对应实际手机dp分辨率下的像素值
     */
    public static int dpSuit2Int(int dp) {
        return (int) (dp * WIDTH_PIXELS / DENSITY / 360.0f * DENSITY + 0.5f);
    }

    public static int getScreenHeight(Context context) {
        return context.getApplicationContext().getResources().getDisplayMetrics().heightPixels;
    }

    public static int getScreenWidth(Context context) {
        return context.getApplicationContext().getResources().getDisplayMetrics().widthPixels;
    }
}
