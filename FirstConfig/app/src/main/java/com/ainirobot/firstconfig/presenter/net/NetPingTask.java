/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

package com.ainirobot.firstconfig.presenter.net;

import android.util.Log;

import com.ainirobot.firstconfig.utils.IOUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class NetPingTask implements Runnable{
    private static final String TAG = NetPingTask.class.getSimpleName();

    private static final long PING_TIME_OUT = 5 * 1000;
    private volatile boolean gotDelayValue = false;
    private volatile boolean isTimeOut = false;
    @Override
    public void run() {
        Process p = null;
        try {
            p = Runtime.getRuntime().exec("/system/bin/ping -c 4 " + "www.baidu.com");
            new RealThread(p).start();
            try {
                Thread.sleep(PING_TIME_OUT);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            Log.d(TAG, "gotDelayValue = "+ gotDelayValue);
            if(!gotDelayValue){
                isTimeOut = true;
                Log.d(TAG, "ping time out above 5s ,check your net please!");
//                result.setSecurityLevel(CheckResult.LEVEL_HIGH);
//                result.setTips("ping time out above 5s ,check your net please!");
//                mScanListener.onWifiScanNodeComplete(IScanStrategy.Node.PING,result);
            }
        } catch (IOException e) {
            e.printStackTrace();
//            result.setSecurityLevel(CheckResult.LEVEL_HIGH);
//            result.setTips("ping net IOException");
//            mScanListener.onWifiScanNodeComplete(IScanStrategy.Node.PING,result);
            Log.d(TAG, "ping net IOException");
        }
    }

    class RealThread extends Thread{
        private Process process;
        RealThread(Process p){
            this.process = p;
        }

        @Override
        public void run() {
            super.run();
            BufferedReader buf = null;
            try {
                Log.d(TAG, "RealThread run .. ");
                buf = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String str = "";
                Log.d(TAG, "buf.readline = "+buf.readLine());
                if(isTimeOut){
                    return;
                }
                while ((str = buf.readLine()) != null) {
                    if (str.contains("avg")) {
                        int i = str.indexOf("/", 20);
                        int j = str.indexOf(".", i);
                        Log.d(TAG, "delay:" + str.substring(i + 1, j));
                        gotDelayValue = true;
//                        result.setSecurityLevel(CheckResult.LEVEL_SAFE);
//                        mScanListener.onWifiScanNodeComplete(IScanStrategy.Node.PING,result);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                IOUtils.close(buf);
            }
        }
    }
}
