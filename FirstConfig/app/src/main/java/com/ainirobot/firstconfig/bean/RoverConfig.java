/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.firstconfig.bean;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.firstconfig.FirstConfigApplication;

import java.io.Serializable;

public class RoverConfig implements Serializable {

    public static final int NORMAL = 0;
    public static final int RISKY = 1;
    public static final int UNSAFE = 2;
    public static final int UNRECOGNIZED = -1;

    public static final int DEVICE_CUSTOM = 0;
    public static final int DEVICE_KTV = 1;
    public static final int DEVICE_MESSIA_LH = 2;

    public static final int SCENES_DEFAULT = 0;
    public static final int SCENES_KTV = 1;

    private String rosIp;
    private boolean enableCamera;
    private int deviceType = 2;//DEVICE_CUSTOM = 0; DEVICE_KTV = 1; DEVICE_MESSIA_LH = 2;
    private int scenesType = 0;//SCENES_DEFAULT = 0; SCENES_KTV = 1;
    private boolean enableFishEye = false;
    private boolean enableRgbd = false;
    private boolean enableIR = false;
    private boolean enableSonar = false;
    private boolean openMaxLostDistance;
    private boolean enableObstaclesAvoid;
    private double avoidStopDetectionDistance;
    private boolean enableVision;
    private String serverIp;
    private boolean enableKeepRight;
    private boolean enableMultiRobot;
    private int multiRobotServerPort;
    private long multiRobotClientId;

    private boolean mapWithRecord;
    private boolean recordMono;
    private boolean recordRgbd;
    private int lethalRadius;
    //相遇防碰撞
    private boolean enableCollisionAvoid;
    //单通防卡死
    private boolean enableStuckAvoid;
    //机器人Rgbd避障
    private boolean enableRgbdAvoidObs;
    //深度相机高精度模式（优化小物体避障）
    private boolean enableRgbdHighAccuracy;
    //是否使用Target定位
    private boolean enableTargetLocate;
    //是否使用寻线功能
    private boolean enableLineTracking;
    //防跌落
    private boolean enableCheckCliff;
    //地图障碍物覆盖
    private boolean enableConveringMapObstacles;
    //巡线路线宽度限制
    private boolean enablePathWidthLimit;
    //定位丢失后,盲走的最大距离的档位, 三个档位,取值Definition.LOST_MAX_DISTANCE.RANGE_HIGH 　
    private float positionLostMaxRange;
    //调整rgbd障碍物高度阈值偏差
    private double rgbd_obs_height_threshold_offset;
    //多机障碍物点功能，用于引导路径绕开障碍物
    @RoverConfigAnnotation("enable_graph_leading_planner_obspoints")
    private boolean enableGraphLeadingPlannerObsPoints;
    //enableGraphLeadingPlannerObsPoints的二级菜单——路径规划等待时长
    private float graphLeadingPlannerWaitTime;
    private float graphLeadingPlannerWaitTimeScale;
    //局部地图人腿检测等级，默认为0 [0： 为无膨胀, 1：仅非致命膨胀, 2：致命与非致命组合膨胀]
    @RoverConfigAnnotation("legs_inflation_level")
    private int localMapLegsInflationLevel;
    //是否增加机器人自转速度
    private boolean increaseRotationSelfSpeed;

    public double getRgbd_obs_height_threshold_offset() {
        return rgbd_obs_height_threshold_offset;
    }

    public void setRgbd_obs_height_threshold_offset(double rgbd_obs_height_threshold_offset) {
        this.rgbd_obs_height_threshold_offset = rgbd_obs_height_threshold_offset;
    }

    public boolean isMapWithRecord() {
        return mapWithRecord;
    }

    public void setMapWithRecord(boolean mapWithRecord) {
        this.mapWithRecord = mapWithRecord;
    }

    public boolean isRecordMono() {
        return recordMono;
    }

    public void setRecordMono(boolean recordMono) {
        this.recordMono = recordMono;
    }

    public boolean isRecordRgbd() {
        return recordRgbd;
    }

    public void setRecordRgbd(boolean recordRgbd) {
        this.recordRgbd = recordRgbd;
    }

    public int getLethalRadius() {
        return lethalRadius;
    }

    public void setLethalRadius(int lethalRadius) {
        this.lethalRadius = lethalRadius;
    }
//    public RoverConfig() {
//    }

    public RoverConfig(String rosIp) {
        this.rosIp = rosIp;
    }

    public void setRosIp(String rosIp) {
        this.rosIp = rosIp;
    }

    public String getRosIp() {
        return rosIp;
    }

    public boolean isEnableCamera() {
        return enableCamera;
    }

    public int getDeviceType() {
        return deviceType;
    }


    public int getScenesType() {
        return scenesType;
    }

//    public void setRosIp(String ip) {
//        this.rosIp = ip;
//    }

    public void setEnableCamera(boolean enableCamera) {
        this.enableCamera = enableCamera;
    }

    public void setDeviceType(int deviceType) {
        this.deviceType = deviceType;
    }


    public void setScenesType(int scenesType) {
        this.scenesType = scenesType;
    }

    public boolean isEnableFishEye() {
        return enableFishEye;
    }

    public void setEnableFishEye(boolean enableFishEye) {
        this.enableFishEye = enableFishEye;
    }

    public boolean isEnableRgbd() {
        return enableRgbd;
    }

    public void setEnableRgbd(boolean enableRgbd) {
        this.enableRgbd = enableRgbd;
    }

    public boolean isEnableIR() {
        return enableIR;
    }

    public void setEnableIR(boolean enableIR) {
        this.enableIR = enableIR;
    }

    public boolean isEnableSonar() {
        return enableSonar;
    }

    public void setEnableSonar(boolean enableSonar) {
        this.enableSonar = enableSonar;
    }

    public boolean isOpenMaxLostDistance() {
        return openMaxLostDistance;
    }

    public void setOpenMaxLostDistance(boolean openMaxLostDistance) {
        this.openMaxLostDistance = openMaxLostDistance;
    }

    public boolean isEnableObstaclesAvoid() {
        return enableObstaclesAvoid;
    }

    public void setEnableObstaclesAvoid(boolean enableObstaclesAvoid) {
        this.enableObstaclesAvoid = enableObstaclesAvoid;
    }

    public boolean isEnableRgbdHighAccuracy() {
        return enableRgbdHighAccuracy;
    }

    public void setEnableRgbdHighAccuracy(boolean enableRgbdHighAccuracy) {
        this.enableRgbdHighAccuracy = enableRgbdHighAccuracy;
    }

    public double getAvoidStopDetectionDistance() {
        return avoidStopDetectionDistance;
    }

    public void setAvoidStopDetectionDistance(double avoidStopDetectionDistance) {
        this.avoidStopDetectionDistance = avoidStopDetectionDistance;
    }

    public void setEnableVision(boolean enableVision) {
        this.enableVision = enableVision;
    }

    public boolean isEnableVision() {
        return enableVision;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public boolean isEnableKeepRight() {
        return enableKeepRight;
    }

    public void setEnableKeepRight(boolean enableKeepRight) {
        this.enableKeepRight = enableKeepRight;
    }

    public boolean isEnableMultiRobot() {
        return enableMultiRobot;
    }

    public void setEnableMultiRobot(boolean enableMultiRobot) {
        this.enableMultiRobot = enableMultiRobot;
    }

    public int getMultiRobotServerPort() {
        return multiRobotServerPort;
    }

    public void setMultiRobotServerPort(int multiRobotServerPort) {
        this.multiRobotServerPort = multiRobotServerPort;
    }

    public long getMultiRobotClientId() {
        return multiRobotClientId;
    }

    public void setMultiRobotClientId(long multiRobotClientId) {
        this.multiRobotClientId = multiRobotClientId;
    }

    public boolean isEnableCollisionAvoid() {
        return enableCollisionAvoid;
    }

    public void setEnableCollisionAvoid(boolean enableCollisionAvoid) {
        this.enableCollisionAvoid = enableCollisionAvoid;
    }

    public boolean isEnableStuckAvoid() {
        return enableStuckAvoid;
    }

    public void setEnableStuckAvoid(boolean enableStuckAvoid) {
        this.enableStuckAvoid = enableStuckAvoid;
    }

    public boolean isEnableRgbdAvoidObs() {
        return enableRgbdAvoidObs;
    }

    public void setEnableRgbdAvoidObs(boolean enableRgbdAvoidObs) {
        this.enableRgbdAvoidObs = enableRgbdAvoidObs;
    }

    public boolean isEnableTargetLocate() {
        return enableTargetLocate;
    }

    public void setEnableTargetLocate(boolean enableTargetLocate) {
        this.enableTargetLocate = enableTargetLocate;
    }

    public boolean isEnableLineTracking() {
        return enableLineTracking;
    }

    public void setEnableLineTracking(boolean enableLineTracking) {
        this.enableLineTracking = enableLineTracking;
        try {
            RobotSettings.setRobotInt(FirstConfigApplication.getAppContext(),
                    "robot_setting_navigation_line_tracking",
                    enableLineTracking ? 1 : 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isEnableCheckCliff() {
        return enableCheckCliff;
    }

    public void setEnableCheckCliff(boolean enableCheckCliff) {
        this.enableCheckCliff = enableCheckCliff;
    }

    public boolean isEnableConveringMapObstacles() {
        return enableConveringMapObstacles;
    }

    public void setEnableConveringMapObstacles(boolean enableConveringMapObstacles) {
        this.enableConveringMapObstacles = enableConveringMapObstacles;
    }

    public boolean isEnablePathWidthLimit() {
        return enablePathWidthLimit;
    }

    public void setEnablePathWidthLimit(boolean enablePathWidthLimit) {
        this.enablePathWidthLimit = enablePathWidthLimit;
    }

    public float getPositionLostMaxRange() {
        return positionLostMaxRange;
    }

    public void setPositionLostMaxRange(float positionLostMaxRange) {
        this.positionLostMaxRange = positionLostMaxRange;
    }

    public boolean isEnableGraphLeadingPlannerObsPoints() {
        return enableGraphLeadingPlannerObsPoints;
    }

    public void setEnableGraphLeadingPlannerObsPoints(boolean enable) {
        this.enableGraphLeadingPlannerObsPoints = enable;
    }

    public float getGraphLeadingPlannerWaitTime() {
        return graphLeadingPlannerWaitTime;
    }

    public void setGraphLeadingPlannerWaitTime(float waitTime) {
        this.graphLeadingPlannerWaitTime = waitTime;
    }

    public float getGraphLeadingPlannerWaitTimeScale() {
        return graphLeadingPlannerWaitTimeScale;
    }

    public void setGraphLeadingPlannerWaitTimeScale(float waitTimeScale) {
        this.graphLeadingPlannerWaitTimeScale = waitTimeScale;
    }

    public int getLocalMapLegsInflationLevel() {
        return localMapLegsInflationLevel;
    }

    public void setLocalMapLegsInflationLevel(int level) {
        this.localMapLegsInflationLevel = level;
    }

    public boolean isEnableIncreaseRotationSelfSpeed() {
        return increaseRotationSelfSpeed;
    }

    public void setEnableIncreaseRotationSelfSpeed(boolean enable) {
        this.increaseRotationSelfSpeed = enable;
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("RoverConfig={ ");
        sb.append("rosIp=");
        sb.append(rosIp);
        sb.append(", enableCamera=");
        sb.append(enableCamera);
        sb.append(", deviceType=");
        sb.append(deviceType);
        sb.append(", scenesType=");
        sb.append(scenesType);
        sb.append(", enableFishEye=");
        sb.append(enableFishEye);
        sb.append(", enableRgbd=");
        sb.append(enableRgbd);
        sb.append(", enableIR=");
        sb.append(enableIR);
        sb.append(", enableSonar=");
        sb.append(enableSonar);
        sb.append(", enableObstaclesAvoid=");
        sb.append(enableObstaclesAvoid);
        sb.append(", openMaxLostDistance=");
        sb.append(openMaxLostDistance);
        sb.append(", avoidStopDetectionDistance=");
        sb.append(avoidStopDetectionDistance);
        sb.append(", enableVision=");
        sb.append(enableVision);
        sb.append(", serverIp=");
        sb.append(serverIp);
        sb.append(", enableKeepRight=");
        sb.append(enableKeepRight);
        sb.append(", enableMultiRobot=");
        sb.append(enableMultiRobot);
        sb.append(", multiRobotServerPort=");
        sb.append(multiRobotServerPort);
        sb.append(", multiRobotClientId=");
        sb.append(multiRobotClientId);
        sb.append(", enableCollisionAvoid=");
        sb.append(enableCollisionAvoid);
        sb.append(", enableStuckAvoid=");
        sb.append(enableStuckAvoid);
        sb.append(", enableRgbdAvoidObs=");
        sb.append(enableRgbdAvoidObs);
        sb.append(", enableRgbdHighAccuracy=");
        sb.append(enableRgbdHighAccuracy);
        sb.append(", enableTargetLocate=");
        sb.append(enableTargetLocate);
        sb.append(", enableLineTracking=");
        sb.append(enableLineTracking);
        sb.append(", enableCheckCliff=");
        sb.append(enableCheckCliff);
        sb.append(", enableConveringMapObstacles=");
        sb.append(enableConveringMapObstacles);
        sb.append(", enablePathWidthLimit=");
        sb.append(enablePathWidthLimit);
        sb.append(", positionLostMaxRange=");
        sb.append(positionLostMaxRange);
        sb.append(", rgbd_obs_height_threshold_offset =");
        sb.append(rgbd_obs_height_threshold_offset);
        sb.append(", enableGraphLeadingPlannerObsPoints =");
        sb.append(enableGraphLeadingPlannerObsPoints);
        sb.append(", localMapLegsInflationLevel =");
        sb.append(localMapLegsInflationLevel);
        sb.append(", increaseRotationSelfSpeed =");
        sb.append(increaseRotationSelfSpeed);
        sb.append(", graphLeadingPlannerWaitTime =");
        sb.append(graphLeadingPlannerWaitTime);
        sb.append(", graphLeadingPlannerWaitTimeScale =");
        sb.append(graphLeadingPlannerWaitTimeScale);
        sb.append(" }");
        return sb.toString();
    }
}
