package com.ainirobot.firstconfig.ui.fragment;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.databinding.LanguageBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.LanguageViewModel;


import java.util.ArrayList;
import java.util.List;

/**
 * 配置语言
 */
public class ConfigLanguageFragment extends BaseFragment<LanguageViewModel, LanguageBinding> {

    private static final String TAG = ConfigLanguageFragment.class.getSimpleName();

    private MyAdapter mAdapter;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_config_language;
    }

    @Override
    protected void initData() {

        if (ProductInfo.isOverSea()){
            mBinding.title.setText("Language");
            mBinding.buttNext.setText("Next");
        }
        mBinding.setClickListener(this);
        mAdapter = new MyAdapter(getActivity());
        mBinding.lvLanguage.setAdapter(mAdapter);
        mBinding.lvLanguage.setOnItemClickListener((parent, view, position, id) -> {
            LanguageBean item = mAdapter.getItem(position);
            Log.e(TAG, "languageWord = " + item.getLangName() + "  mLanguageCode = " + item.getLangCode());
            mViewModel.setDefaultCode(item.getLangCode());
            selectPosition(position);
        });

        mViewModel.getLanguageData().observe(this, languageBeanList -> {
            mAdapter.updateData(languageBeanList);
            mAdapter.notifyDataSetChanged();
        });
    }

    private void selectPosition(int clickPosition) {
        int count = mAdapter.getCount();
        for(int i = 0 ; i < count; i++){
            mAdapter.getItem(i).setIsDefault(i == clickPosition ?
                    LanguageBean.UseState.DEFAULT
                    : LanguageBean.UseState.NOT_DEFAULT);
        }
        mAdapter.notifyDataSetChanged();
    }


    /**
     * 解析本地语言列表,多语言需求修改为CoreService 统一本地解析,统一存储
     */
//    private void initLanguage() {
//        try {
//            list.clear();
//            Resources res = getResources();
//            XmlResourceParser xrp = res.getXml(R.xml.set_language);
//            while (xrp.getEventType() != XmlResourceParser.END_DOCUMENT) {
//                if (xrp.getEventType() == XmlResourceParser.START_TAG) {
//                    String name = xrp.getName();
//                    if (name.equals("language")) {
//                        //0，标识id，1标识名称
//                        String code = SharedPrefUtil.getInstance().getFirstConfigLanguageCode();
//                        String defaultCode = TextUtils.isEmpty(code) ? DEFAULT_LANGUAGE : code;
//                        boolean isDefault = defaultCode.equals(xrp.getAttributeValue(0));
//                        list.add(new Language(xrp.getAttributeValue(0),xrp.getAttributeValue(1),isDefault));
//                    }
//                }
//                xrp.next();
//            }
//        } catch (Exception e) {
//            // TODO Auto-generated catch block
//            e.printStackTrace();
//        }
//
//    }



    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<LanguageBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);

        }

        public List<LanguageBean> getData(){
            return mData;
        }

        public void updateData(List<LanguageBean> list){
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public LanguageBean getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new MyAdapter.ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvLanguage = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (MyAdapter.ViewHolder) convertView.getTag();
            }
            LanguageBean item = getItem(position);
            holder.tvLanguage.setText(item.getLangName());
            holder.imSelect.setImageResource(item.isDefaultLanguage() ? R.drawable.select : R.drawable.unselect);

//            if (mTag != 2 && mLanguageShown.equals(mData.get(position))) {
//                holder.imSelect.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.select));
//            } else if (mTag == 2 && mLanguagePostion == position) {
//                holder.imSelect.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.select));
//            } else {
//                holder.imSelect.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.unselect));
//            }

            return convertView;
        }

//        public void setTag(int tag, int postion) {
//            mLanguagePostion = postion;
//            mTag = tag;
//        }

        class ViewHolder {
            TextView tvLanguage;
            ImageView imSelect;
        }
    }

}
