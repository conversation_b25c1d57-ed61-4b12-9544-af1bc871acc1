package com.ainirobot.firstconfig.control;

import android.util.Log;

import com.ainirobot.coreservice.client.ApiListener;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.speech.SkillApi;
import com.ainirobot.coreservice.client.speech.entity.TTSEntity;
import com.ainirobot.firstconfig.FirstConfigApplication;

public class SpeechManager {

    public static final String TAG = SpeechManager.class.getSimpleName();
    private volatile boolean mIsSpeechConnected = false;
    private boolean isUseLocalSpeech = true;
    private SkillApi mSkillApi = null;


    private SpeechManager(){
    }

    private static class InstanceHolder {
        static final SpeechManager instance = new SpeechManager();
    }

    public static SpeechManager getInstance() {
        return InstanceHolder.instance;
    }
    /**
     * connect  speech to use asr .
     */
    public void connectSpeechService() {
        mSkillApi = new SkillApi();
        mSkillApi.connectApi(FirstConfigApplication.getAppContext(), new ApiListener() {
            @Override
            public void handleApiDisabled() {
                mIsSpeechConnected = false;
                Log.d(TAG, "SkillApi handleApiDisabled ");
            }

            @Override
            public void handleApiConnected() {
                Log.d(TAG, "SkillApi connected ---------  ");
                if (mSkillApi != null) {
                    mIsSpeechConnected = true;
                    mSkillApi.setRecognizable(false);
                }
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "SkillApi handleApiDisconnected --------- ");
                mIsSpeechConnected = false;
                mSkillApi = null;
            }
        });
    }

    /**
     * disconnect  speech to use asr
     */
    public void disConnectSpeechService() {
        if (mSkillApi == null || !mIsSpeechConnected) {
            Log.d(TAG,"disConnectSpeechService return ....");
            return;
        }
        openRecognizable();
        Log.d(TAG, "disconnect Speech Api");
        mSkillApi.disconnectApi();
    }


    private void openRecognizable() {
        if (mSkillApi == null || !mIsSpeechConnected) {
            return;
        }
        mSkillApi.setRecognizable(true);
    }

    public void disableLocalSpeech(boolean status) {
        Log.d(TAG, "disableLocalSpeech:" + status);
        isUseLocalSpeech = status;
    }

    public void speechPlayRemoteText(String text) {
        if (mSkillApi == null || !mIsSpeechConnected) {
            return;
        }
        mSkillApi.playText(new TTSEntity(text), null);
    }

    public void speechPlayText(String text) {
        Log.d(TAG,  "speechPlayText : " + text + " , mIsSpeechConnected :" + mIsSpeechConnected);
        if (mSkillApi == null || !mIsSpeechConnected) {
            return;
        }

        if (!isUseLocalSpeech) {
            return;
        }
        mSkillApi.playText(new TTSEntity(text), null);
    }
    public boolean speechPlayText(String text, TextListener listener) {
        Log.d(TAG,  "speechPlayText : " + text + " , mIsSpeechConnected :" + mIsSpeechConnected);
        if (mSkillApi == null || !mIsSpeechConnected) {
            return false;
        }

        if (!isUseLocalSpeech) {
            return false;
        }
        mSkillApi.playText(new TTSEntity(text), listener);
        return true;
    }


}
