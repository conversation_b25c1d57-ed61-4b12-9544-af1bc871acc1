/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.ime;

import android.provider.Settings;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.control.state.StateMachine;
import com.ainirobot.firstconfig.utils.FileUtil;

import java.io.File;
import java.io.IOException;

public class GboardConfigLoader implements LoadImeConfigStrategy {
    public static final String IMENAME = "com.google.android.inputmethod.latin";
    private static final String TAG = GboardConfigLoader.class.getName();
    private String mType;

    public GboardConfigLoader(String type) {
        mType = type;
    }

    @Override
    public String getName() {
        return TAG;
    }

    @Override
    public boolean prepare() {
        //TODO choose different language tar file
        String name = "gboard_" + mType + ".tar";
        File file = FileUtil.copyAssetsToSdcard("input", name);
        return file.exists();
    }

    @Override
    public boolean load() {
        try {
            Settings.Global.putInt(FirstConfigApplication.getAppContext().getContentResolver(),
                    Settings.Global.DEVICE_PROVISIONED, 1);

            //TODO use sed cmd, modify config
            String cmdline = "bmgr enable true ; "
                    + "bmgr transport android/com.android.internal.backup.LocalTransport | grep -r Selected && sleep 3s ; "
                    + "tar -xmvf /sdcard/robot/local/gboard_" + mType + ".tar -C / ; "
                    + "bmgr restore 1 com.google.android.inputmethod.latin";
            Runtime.getRuntime().exec(new String[]{"/system/bin/sh", "-c", cmdline}).waitFor();
            Log.d(TAG, "load end");
            return true;
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public boolean recycle() {
        try {
            String cmdline = "bmgr enable false ; "
                    + "am force-stop com.google.android.inputmethod.latin";
            Runtime.getRuntime().exec(new String[]{"/system/bin/sh", "-c", cmdline}).waitFor();
            Log.d(TAG, "recycle end");
            return true;
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }
}
