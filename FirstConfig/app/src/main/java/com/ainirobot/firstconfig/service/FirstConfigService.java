/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.service;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.InspectionResult;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.GlobalData;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.bi.BiInspectionReport;
import com.ainirobot.firstconfig.control.InspectionViewHelper;
import com.ainirobot.firstconfig.control.LampLightManager;
import com.ainirobot.firstconfig.control.SpeechManager;
import com.ainirobot.firstconfig.control.state.StateMachine;
import com.ainirobot.firstconfig.proxy.SystemApiUtil;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;
import org.simple.eventbus.ThreadMode;

import java.util.List;

public class FirstConfigService extends Service {
    private static final String TAG = FirstConfigService.class.getSimpleName();
    private static final String BROAD_CAST_FIRST_CONFIG_FINISH = "com.ainirobot.firstconfig.config_finish";
    public static final String INTENT_INSPECTION_SKIP = "intent_inspection_skip";

    private StateMachine mStateMachine;

    @Override
    public void onCreate() {
        Log.d(TAG, "onCreate");

        SystemApiUtil.connectSystemApi();
        mStateMachine = new StateMachine();
        mStateMachine.start();

        // 设置light color监听
        LampLightManager.getInstance().startLampLightListener();
        EventBus.getDefault().register(this);

        //跳过自检监听
        registerReceiver(mBroadcastReceiver, new IntentFilter(INTENT_INSPECTION_SKIP));
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        unregisterReceiver(mBroadcastReceiver);
    }


    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_REFRESH_QRCODE)
    private void onEventBus_onRefreshQrcode(boolean status) {
        Log.d(TAG, "onRefreshQrcode:" + status);
        mStateMachine.refreshQrCode();
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_REFRESH_SETTINGS_SCENARIO_LIST)
    private void onEventBus_onRefreshSettingScenario(boolean status) {
        Log.d(TAG, "onRefreshSettingScenario:" + status);
        mStateMachine.refreshSettingScenario();
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO)
    private void onEventBus_onUploadSettingScenario(String platformId) {
        Log.d(TAG, "onUploadSettingScenario:" + platformId);
        mStateMachine.uploadSettingScenario(platformId);
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_UPDATE_SETTINGS_SCENARIO)
    private void onEventBus_onSetSettingsScenario(boolean skip) {
        Log.d(TAG, "setSettingsScenario:" + skip);
        mStateMachine.setSettingsScenario(skip);
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVEVTBUS_TAG_INIT_FINISH)
    private void onEventBus_onInitFinish(boolean status) {
        Log.d(TAG, "FirstConfig init finish ");
        mStateMachine.finishLanguageState();
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_BACK_PRE_PAGE)
    private void startPreState(boolean status){
        mStateMachine.backPreState();
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_CHECK_INSPECTION_VIEW_MODEL)
    private void onEventBus_checkInspectionViewModel(boolean status){
        Log.d(TAG, "onEventBus_checkInspectionViewModel:");
        InspectionViewHelper.checkIsNeedShowWebView(mStateMachine);
    }

    @Subscriber(mode = ThreadMode.ASYNC, tag = FirstConfigDef.EVENTBUS_TAG_FIRST_CONFIG_FINISH)
    private void onEventBus_onFirstConfigFinish(int type) {

        LampLightManager.getInstance().unRegisterLightListener();
        SpeechManager.getInstance().disConnectSpeechService();
        SystemApi.getInstance().disconnectApi();

        SystemUtils.enableStatusBar(); // 招财启用支持三指下拉,后续Mini也如此支持，无需根据model号判断
        Settings.Global.putInt(FirstConfigApplication.getAppContext().getContentResolver(),
                FirstConfigDef.FIRST_ACTIVATION, FirstConfigDef.FIRST_ACTIVATION_VALUE);
        // setHomeLaunchComponent,  com.android.settings.FallbackHome依赖该COMPONENT格式,否则FallbackHome 报NullPointer.
        SettingsUtil.putString(FirstConfigApplication.getAppContext(),
                SettingsUtil.ROBOT_SETTING_HOME_LAUNCHER_COMPONENT, FirstConfigDef.HOME_LAUNCHER_COMPONENT);
        // 判断是否启动Home Service
        SystemUtils.startHomeService(type);
        SystemUtils.endFirstConfig(FirstConfigDef.HOME_LAUNCHER_CLASSNAME);

        broadcastEndFirstConfig();

        SharedPrefUtil.getInstance().clearConfigComplete();
        stopSelf();
        EventBus.getDefault().post(true,FirstConfigDef.EVENTBUS_TAG_FINISH_ACTIVITY);
    }

    private static void broadcastEndFirstConfig() {
        Log.d(TAG,"This will complete FirstConfig and stop FirstConfigService ======= ");
        //首次配置完成广播
        Intent intent = new Intent();
        intent.setAction(BROAD_CAST_FIRST_CONFIG_FINISH);
        FirstConfigApplication.getAppContext().sendBroadcast(intent);
    }

    @Subscriber(mode = ThreadMode.POST, tag = FirstConfigDef.EVENTBUS_TAG_NEW_REQUEST)
    private void onEventBus_onNewRequest(@NonNull Bundle bundle) {
        Log.d(TAG, "onNewRequest id:" + bundle.getInt(FirstConfigDef.BUNDLE_ID)
                + ", intent:" + bundle.getString(FirstConfigDef.BUNDLE_INTENT)
                + ", param:" + bundle.getString(FirstConfigDef.BUNDLE_PARAM));
    }

    final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                if(INTENT_INSPECTION_SKIP.equals(intent.getAction())){
                    onInspectionSkip();
                }
            }
        }
    };

    private void onInspectionSkip(){
        Log.d(TAG,"onInspectionSkip");
        resetAutoRetryRebootValue();
        //onSuccess();
        //reportInspectBi(bean, s, BiInspectionReport.ACTION_SUCCESS);
        Log.d(TAG, "handleInspectResult:success: isShowWebViewInspectUI=" +
                GlobalData.INSTANCE.isShowWebViewInspectUI());
        if (GlobalData.INSTANCE.isShowWebViewInspectUI()) {
            Log.d(TAG, "handleInspectResult:success: Send event to Model!");
            EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_INSPECTION_SUCCESS);
        } else {
            showBindSuccess();
        }

    }

    private void showBindSuccess() {
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_BIND_SUCCESS),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

    /**
     * 如果首次自检失败，自动重启一次，随后只有自检成功再重置状态。
     */
    private void resetAutoRetryRebootValue(){
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct()){
            SettingsUtil.putInt(FirstConfigApplication.getAppContext(), SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                    FirstConfigDef.SETTING_CAN_REBOOT_ROBOT);
        }
    }

}
