package com.ainirobot.firstconfig.control.state;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.firstconfig.FirstConfigDef;

import org.simple.eventbus.EventBus;

public class SettingsScenarioUploadState extends BaseState {

    private String platformId;

    SettingsScenarioUploadState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
        platformId = (String) data;
    }

    @Override
    protected void doAction() {
        if (TextUtils.isEmpty(platformId)) {
            onSuccess();
            return;
        }
        SystemApi.getInstance().uploadSettingsScenario(Definition.MODULE_REQ_ID, platformId, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                if (TextUtils.equals(message, Definition.SUCCEED)) {
                    EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO_RESULT);
                    onSuccess();
                } else {
                    EventBus.getDefault().post(false, FirstConfigDef.EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO_RESULT);
                }
            }
        });
    }

    @Override
    protected BaseState getNextState() {
        if (TextUtils.isEmpty(platformId)) {
            return StateEnum.INSPECT.getState();
        }
        return StateEnum.SETTINGS_SCENARIO_INFO.getState();
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.CONFIGURE.getState();
    }

    @Override
    protected void exit() {
        super.exit();
        platformId = null;
    }
}
