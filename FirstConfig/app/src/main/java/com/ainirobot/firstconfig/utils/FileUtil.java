package com.ainirobot.firstconfig.utils;
import android.annotation.TargetApi;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.os.storage.StorageManager;
import android.text.TextUtils;
import android.util.Log;


import com.ainirobot.base.ApplicationWrapper;
import com.ainirobot.firstconfig.FirstConfigApplication;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import androidx.annotation.NonNull;


public class FileUtil {

    private static final String TAG = FileUtil.class.getSimpleName();

    private static final int BUFFER_SIZE = 1024;

    // 判断SD卡是否被挂载
    public static boolean isSDCardMounted() {
        // return Environment.getExternalStorageState().equals("mounted");
        return Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED);
    }

    // 获取SD卡的根目录
    public static String getSDCardBaseDir() {
        if (isSDCardMounted()) {
            return Environment.getExternalStorageDirectory().getAbsolutePath();
        }
        return null;
    }

    // 获取SD卡的完整空间大小，返回MB
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
    public static long getSDCardSize() {
        if (isSDCardMounted()) {
            StatFs fs = new StatFs(getSDCardBaseDir());
            long count = fs.getBlockCountLong();
            long size = fs.getBlockSizeLong();
            return count * size / 1024 / 1024;
        }
        return 0;
    }

    // 获取SD卡的剩余空间大小
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
    public static long getSDCardFreeSize() {
        if (isSDCardMounted()) {
            StatFs fs = new StatFs(getSDCardBaseDir());
            long count = fs.getFreeBlocksLong();
            long size = fs.getBlockSizeLong();
            return count * size / 1024 / 1024;
        }
        return 0;
    }

    // 获取SD卡的可用空间大小
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
    public static long getSDCardAvailableSize() {
        if (isSDCardMounted()) {
            StatFs fs = new StatFs(getSDCardBaseDir());
            long count = fs.getAvailableBlocksLong();
            long size = fs.getBlockSizeLong();
            return count * size / 1024 / 1024;
        }
        return 0;
    }

    // 往SD卡的公有目录下保存文件
    public static boolean saveFileToSDCardPublicDir(byte[] data, String type, String fileName) {
        BufferedOutputStream bos = null;
        if (isSDCardMounted()) {
            File file = Environment.getExternalStoragePublicDirectory(type);
            try {
                bos = new BufferedOutputStream(new FileOutputStream(new File(file, fileName)));
                bos.write(data);
                bos.flush();
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (bos != null)
                        bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    // 往SD卡的自定义目录下保存文件
    public static boolean saveFileToSDCardCustomDir(byte[] data, String dir, String fileName) {
        BufferedOutputStream bos = null;
        if (isSDCardMounted()) {
            File file = new File(dir);
            if (!file.exists()) {
                file.mkdirs();// 递归创建自定义目录
            }
            try {
                bos = new BufferedOutputStream(new FileOutputStream(new File(file, fileName)));
                bos.write(data);
                bos.flush();
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (bos != null)
                        bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    // 往SD卡的私有Files目录下保存文件
    public static boolean saveFileToSDCardPrivateFilesDir(byte[] data, String type, String fileName, Context context) {
        BufferedOutputStream bos = null;
        if (isSDCardMounted()) {
            File file = context.getExternalFilesDir(type);
            try {
                bos = new BufferedOutputStream(new FileOutputStream(new File(file, fileName)));
                bos.write(data);
                bos.flush();
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (bos != null)
                        bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    // 往SD卡的私有Cache目录下保存文件
    public static boolean saveFileToSDCardPrivateCacheDir(byte[] data, String fileName, Context context) {
        BufferedOutputStream bos = null;
        if (isSDCardMounted()) {
            File file = context.getExternalCacheDir();
            try {
                bos = new BufferedOutputStream(new FileOutputStream(new File(file, fileName)));
                bos.write(data);
                bos.flush();
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (bos != null)
                        bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    // 保存bitmap图片到SDCard的私有Cache目录
    public static boolean saveBitmapToSDCardPrivateCacheDir(Bitmap bitmap, String fileName, Context context) {
        if (isSDCardMounted()) {
            BufferedOutputStream bos = null;
            // 获取私有的Cache缓存目录
            File file = context.getExternalCacheDir();

            try {
                bos = new BufferedOutputStream(new FileOutputStream(new File(file, fileName)));
                if (fileName != null && (fileName.contains(".png") || fileName.contains(".PNG"))) {
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, bos);
                } else {
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
                }
                bos.flush();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (bos != null) {
                    try {
                        bos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            return true;
        } else {
            return false;
        }
    }

    // 从SD卡获取文件
    public static byte[] loadFileFromSDCard(String fileDir) {
        BufferedInputStream bis = null;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            bis = new BufferedInputStream(new FileInputStream(new File(fileDir)));
            byte[] buffer = new byte[8 * 1024];
            int c = 0;
            while ((c = bis.read(buffer)) != -1) {
                baos.write(buffer, 0, c);
                baos.flush();
            }
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(baos);
            IOUtils.close(bis);
        }
        return null;
    }



    /**
     * 由apk路径获取PackageInfo
     *
     * @param context
     * @param apkPath
     * @return
     */
    public static PackageInfo getPackageInfo(Context context, String apkPath) {
        try {
            final PackageManager pm = context.getPackageManager();
            return pm.getPackageArchiveInfo(apkPath, 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



    public static File createFile(String parentDir, String finalFileName) {
        File dir = new File(parentDir);
        if (!dir.exists()) {
            boolean made = dir.mkdirs();
            if (!made) {
                return null;
            }
        }
        File file = new File(dir, finalFileName);
        Log.d(TAG, "createFile file absPath : " + file.getAbsolutePath());
        if (!file.exists()) {
            try {
                boolean created = file.createNewFile();
                if (!created) {
                    return null;
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, "createNewFile error : Invalid file path ");
                return null;
            }
        }
        return file;
    }

    public static boolean createDir(String path) {
        File file = new File(path);
        if (file.exists()) {
            return true;
        } else {
            return file.mkdirs();
        }
    }

    /**
     * Reads an InputStream to a file
     *
     * @param input InputStream to read
     * @param file  File into which to store the read data
     * @return <code>true</code> if store success </code> otherwise
     * @note The input stream is not closed
     */
    public static boolean store(@NonNull InputStream input, @NonNull File file) {

        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            byte[] buff = new byte[1024 * 10];
            while (true) {

                int result = input.read(buff);
                if (result == -1) {
                    break;
                }
                fos.write(buff, 0, result);
                Log.d(TAG, "Read file length = " + result);
                fos.flush();
            }
            return true;
        } catch (IOException ignored) {
            ignored.printStackTrace();
            return false;
        } finally {
            IOUtils.close(fos);
        }
    }

    private static final SimpleDateFormat DATE_FORMAT =
            new SimpleDateFormat("yyyy-MM-dd-hh-mm-ss", Locale.CHINA);

    private static final String CACHE_DIR = "robot/cache";
    private static final String VISION_PICTURE_DIR = "Download/vision_picture";
    public static final String DIR_PICTURE = "screenshot";
    public static final String DIR_TAKE_PICTURE = "takePicture";

    private static File getSdcardRoot() {
        return Environment.getExternalStorageDirectory();
    }

    private static File getCacheDir() {
        File root = getSdcardRoot();
        File dir = new File(root, CACHE_DIR);
        if (!dir.exists()) {
            boolean mkdirs = dir.mkdirs();
            Log.d(TAG, "getCacheDir mkdirs result :" + mkdirs);
        }
        return dir;
    }


    public static File getPictureFile(String fileName) {
        return getFile(DIR_PICTURE, fileName);
    }

    public static File getTakePictureFile(String fileName) {
        return getFile(DIR_TAKE_PICTURE, fileName);
    }

    public static File getFile(@NonNull String dirName, String fileName) {
        File cacheDir = getCacheDir();
        File dir = new File(cacheDir, dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        Log.d(TAG, "dir exist:" + dir.exists());

        if (fileName == null) {
            fileName = getCurrentDate() + ".png";
        }
        return createFile(dir.getAbsolutePath(), fileName);
    }

    public static String saveBitmap(File savedFile, Bitmap desBitmap) {
        if (!savedFile.exists()) {
            return "";
        }
        try {
            FileOutputStream fout = new FileOutputStream(savedFile);
            BufferedOutputStream bos = new BufferedOutputStream(fout);
            desBitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return savedFile.getAbsolutePath();
    }

    public static String getCurrentDate() {
        return DATE_FORMAT.format(System.currentTimeMillis());
    }

    public static void deletePictures(final List<String> fileNames) {
        if (fileNames == null || fileNames.size() <= 0) {
            Log.i(TAG, "file name length error, return!!");
            return;
        }
        for (String filename : fileNames) {
            File file = new File(filename);
            if (file.exists() && file.isFile()) {
                Log.i(TAG, "delete file:" + filename);
                file.delete();
            } else {
                Log.i(TAG, "no exits, file name:" + fileNames);
            }
        }
    }

    public static boolean deletePicByPath(String path) {
        boolean result = false;
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            result = file.delete();
        } else {
            Log.i(TAG, "no exits, file name:" + path);
        }
        return result;
    }

    public static void deleteVisionPictures(){
        File visionPictureDir = getVisionPictureDir();
        Log.d(TAG, "getFacePicturePathList visionPictureDir:" + visionPictureDir.getAbsolutePath());
        File[] files = visionPictureDir.listFiles();
        if (files != null && files.length > 0) {
            for (File file : files) {
                file.delete();
            }
        }
    }

    /**
     * /storage/emulated/0/Download/vision_picture/
     */
    private static File getVisionPictureDir() {
        File root = getSdcardRoot();
        return new File(root, VISION_PICTURE_DIR);
    }

    public static ArrayList<String> getFacePicturePathList() {
        ArrayList<String> pictures = new ArrayList<>();

        File visionPictureDir = getVisionPictureDir();
        Log.d(TAG, "getFacePicturePathList visionPictureDir:" + visionPictureDir.getAbsolutePath());
        File[] files = visionPictureDir.listFiles();
        if (files != null && files.length > 0) {
            for (File file : files) {
                pictures.add(file.getAbsolutePath());
            }
        }
        return pictures;
    }

    /**
     * copy assets files 到 sdcard
     */
    private static final String LOCAL_DIR = "robot/local";

    public static File copyAssetsToSdcard(String assetsPath, String fileName) {

        File root = Environment.getExternalStorageDirectory();
        String parentPath = new File(root, LOCAL_DIR).getAbsolutePath();
        Log.d(TAG, "parentPath :" + parentPath);
        createDir(parentPath);
        File file = new File(parentPath, fileName);
        Log.d(TAG, "local file getPath:" + file.getPath());
        if (file.exists()) {
            Log.d(TAG, fileName + " is already exists");
            return file;
        }

        InputStream is = null;
        try {
            is = FirstConfigApplication.getAppContext().getAssets().open(assetsPath + File.separator + fileName);
            FileUtil.saveFileToSDCardCustomDir(inputStreamToByte(is), parentPath, fileName);
        } catch (IOException e) {
            e.printStackTrace();
            Log.d(TAG, "createMetroAudio : " + e.getLocalizedMessage());
        } finally {
            IOUtils.close(is);
        }
        return file;
    }

    /**
     * 将InputStream转换成byte数组
     * @param in InputStream
     * @return byte[]
     * @throws IOException
     */
    public static byte[] inputStreamToByte(InputStream in) throws IOException{

        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] data = new byte[BUFFER_SIZE];
        int count = -1;
        while((count = in.read(data,0,BUFFER_SIZE)) != -1)
            outStream.write(data, 0, count);

        return outStream.toByteArray();
    }

    public static String loadRaw(Context context, int rawFile) {
        InputStream is = null;
        try {
            is = context.getResources().openRawResource(rawFile);
            int total = is.available();
            byte[] bytes = new byte[total];
            int len = is.read(bytes);
            if (total == len) {
                return new String(bytes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.close(is);
        }
        return null;
    }
}