package com.ainirobot.firstconfig.utils;

import android.util.Log;

import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;

public class JsonUtils {

    private static final String TAG = JsonUtils.class.getSimpleName();

    public static String getStringValue(JsonElement jsonElement, String key) {
        JsonPrimitive data = jsonElement.getAsJsonPrimitive();
        try {
            return data.getAsString();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "getIntValue key: " + key);
            return null;
        }
    }

    public static int getIntValue(JsonElement jsonElement, String key) {
        JsonPrimitive data = jsonElement.getAsJsonPrimitive();
        try {
            return data.getAsInt();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "getFloatValue key: " + key);
            return -1;
        }
    }

    public static long getLongValue(JsonElement jsonElement, String key) {
        JsonPrimitive data = jsonElement.getAsJsonPrimitive();
        try {
            return data.getAsLong();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "getFloatValue key: " + key);
            return -1;
        }
    }

    public static double getDoubleValue(JsonElement jsonElement, String key) {
        JsonPrimitive data = jsonElement.getAsJsonPrimitive();
        try {
            return data.getAsDouble();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "getFloatValue key: " + key);
            return -1;
        }
    }

    public static double getFloatValue(JsonElement jsonElement, String key) {
        JsonPrimitive data = jsonElement.getAsJsonPrimitive();
        try {
            return data.getAsFloat();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "getFloatValue key: " + key);
            return -1;
        }
    }
}
