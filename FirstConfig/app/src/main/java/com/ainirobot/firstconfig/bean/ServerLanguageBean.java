package com.ainirobot.firstconfig.bean;

public class ServerLanguageBean {

    private boolean verified;
    private String serverMain;

    public ServerLanguageBean(boolean verified, String serverMain) {
        this.verified = verified;
        this.serverMain = serverMain;
    }

    public boolean isVerified() {
        return verified;
    }

    public void setVerified(boolean verified) {
        this.verified = verified;
    }

    public String getServerMain() {
        return serverMain;
    }

    public void setServerMain(String serverMain) {
        this.serverMain = serverMain;
    }

    @Override
    public String toString() {
        return "ServerLanguageBean{" +
                "verified=" + verified +
                ", serverMain='" + serverMain + '\'' +
                '}';
    }
}
