package com.ainirobot.firstconfig.utils;

import android.text.TextUtils;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.CloudServerBean;
import com.ainirobot.firstconfig.bean.OSTypeBean;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Data: 2023/5/22 19:19
 * Description: CloudUtils
 * 多服务器工具类
 */
public class CloudUtils {
    public static final String BIND_URL_US = "us-bind.orionstar.com";
    public static final String BIND_URL_EU = "bind.orionstar.com";
    //TODO
    public static final String BIND_URL_JP = "jp-bind.orionstar.com";


    /**
     * 各服务器定义
     */
    public static Map<String, CloudInfo> cloudInfoMap = new LinkedHashMap<String, CloudInfo>() {{
        //** 欧洲地区--欧洲云 **
        put(Definition.SERVER_EU, new CloudInfo(BIND_URL_EU, R.string.cloud_europe, Definition.CloudServerZone.DEFAULT));
        //美国地区--美西云
        put(Definition.SERVER_US, new CloudInfo(BIND_URL_US, R.string.cloud_us_west, Definition.CloudServerZone.US));
        //日本地区--日本云
        put(Definition.SERVER_JP, new CloudInfo(BIND_URL_JP, R.string.cloud_jp_server, Definition.CloudServerZone.JP));
        //** 其他地区--欧洲云 **
        put(Definition.SERVER_OTHER, new CloudInfo(BIND_URL_EU, R.string.cloud_other_regions, Definition.CloudServerZone.DEFAULT));
    }};

    /**
     * 系统类型定义
     */
    public static Map<String, OSInfo> osTypeMap = new LinkedHashMap<String, OSInfo>() {{
        put(Definition.OSType.ROBOTOS.getValue(), new OSInfo(R.string.os_robot, Definition.OSType.ROBOTOS));
        put(Definition.OSType.AGENTOS.getValue(), new OSInfo(R.string.os_agent, Definition.OSType.AGENTOS));
    }};

    /**
     * 根据CloudInfoMap获得所有的cloudServerBean
     *
     * @param serverZone 保存的线上环境
     * @param serverLocationCode 保存的地区名称
     */
    public static List<CloudServerBean> getAllCloudServerBean(String serverZone, String serverLocationCode) {
        List<CloudServerBean> cloudServerBeanList = new ArrayList<>();

        for (Map.Entry<String, CloudInfo> infoEntry : cloudInfoMap.entrySet()) {
            String serverName = infoEntry.getKey();
            CloudInfo info = infoEntry.getValue();
            cloudServerBeanList.add(new CloudServerBean(
                    serverName,
                    info.getResName(),
                    isSelectServer(serverZone, serverLocationCode, serverName))
            );
        }
        return cloudServerBeanList;
    }

    public static List<OSTypeBean> getServerType(String currentOSType) {
        List<OSTypeBean> serverList = new ArrayList<>();

        for (Map.Entry<String, OSInfo> infoEntry : osTypeMap.entrySet()) {
            String osType = infoEntry.getKey();
            OSInfo info = infoEntry.getValue();
            serverList.add(new OSTypeBean(
                    osType,
                    info.getResName(),
                    TextUtils.equals(currentOSType, osType))
            );
        }
        return serverList;
    }

    /**
     * 根据地区获得线上环境名称
     */
    public static String getServerZone(String serverCode) {
        CloudInfo cloudInfo = cloudInfoMap.get(serverCode);

        return null == cloudInfo
                ? cloudInfoMap.get(Definition.SERVER_OTHER).getServerZone().getValue()
                : cloudInfo.getServerZone().getValue();
    }

    /**
     * 根据地区，获得对应语言名称
     */
    public static String getServerResName(String serverCode) {
        CloudInfo cloudInfo = cloudInfoMap.get(serverCode);

        return null == cloudInfo
                ? cloudInfoMap.get(Definition.SERVER_OTHER).getResName()
                : cloudInfo.getResName();
    }

    /**
     * 根据地区，获得对应的url
     */
    public static String getServerUrl(String serverCode) {
        CloudInfo cloudInfo = cloudInfoMap.get(serverCode);

        return null == cloudInfo
                ? cloudInfoMap.get(Definition.SERVER_OTHER).getServerUrl()
                : cloudInfo.getServerUrl();
    }

    /**
     * 是否正在使用的地区
     * 根据服务器地区和线上环境名称判断
     *
     * @param serverZone 线上环境名称
     * @param serverCode 保存的地区code
     * @param server     要对比的地区code
     * @return true：正在使用的是该地区
     */
    public static boolean isSelectServer(String serverZone,
                                         String serverCode,
                                         String server) {
        return TextUtils.equals(getServerZone(server), serverZone) && TextUtils.equals(serverCode, server);
    }

    /**
     * 判断两个地区code使用的线上环境是否一致
     *
     * @return true:不一致，切换了
     */
    public static boolean isServerSwitched(String newCode, String localDefaultCode) {
        return !TextUtils.equals(CloudUtils.getServerZone(localDefaultCode), CloudUtils.getServerZone(newCode));
    }

    private static class CloudInfo {
        private final String serverUrl;
        private final int resId;
        private final Definition.CloudServerZone serverZone;

        public CloudInfo(String serverUrl, int resId, Definition.CloudServerZone serverZone) {
            this.serverUrl = serverUrl;
            this.resId = resId;
            this.serverZone = serverZone;
        }

        public String getServerUrl() {
            return serverUrl;
        }

        public String getResName() {
            return ResUtil.getString(resId);
        }

        public Definition.CloudServerZone getServerZone() {
            return serverZone;
        }

    }

    private static class OSInfo {
        private final int resId;
        private final Definition.OSType osType;

        public OSInfo(int resId, Definition.OSType serverType) {
            this.resId = resId;
            this.osType = serverType;
        }

        public String getResName() {
            return ResUtil.getString(resId);
        }

        public Definition.OSType getServerType() {
            return osType;
        }

    }
}
