/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.BindState;
import com.ainirobot.firstconfig.bean.StateData;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
public class CheckingState extends BaseState {
    private static final String TAG = CheckingState.class.getSimpleName();
    private static final long BIND_STATE_CHECKING_INTERVAL = 5 * 1000;
    private static final long BIND_STATE_MAX_RETRY_TIME = 100;

    private BindState mBindState;
    private volatile boolean isStateAlive = false;
    private int mState;
    private int mTimes;

    CheckingState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);

        mBindState = null;
        mState = Definition.BIND_UNKNOWN;
        mTimes = 0;
    }

    @Override
    protected void doAction() {
        isStateAlive = true;
        startBindStateCheckingTimer();
    }

    @Override
    protected BaseState getNextState() {
        switch (mState) {
            case Definition.BIND_SUCCESS:
            case Definition.BIND_NONEED:
                return StateEnum.REPORT_OS_TYPE.getState();
            case Definition.BIND_FAILURE:
                return StateEnum.BINDING.getState();
            default:
                Log.e(TAG, "state err:" + mState);
                return null;
        }
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.CONFIGURE.getState();
    }

    @Override
    protected void exit() {
        super.exit();
        isStateAlive = false;
        DelayTask.cancel(CheckingState.this);
    }

    @Override
    protected Object getData() {
        return mBindState;
    }

    private void startBindStateCheckingTimer() {
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                if (!isStateAlive){
                    return;
                }
                String info = SystemApi.getInstance().getBindInfo();
                mBindState = parseRobotBindInfo(info);
                Log.d(TAG, "handleBindInfo bindState:" + mBindState);
                if (mBindState != null) {
                    mState = mBindState.getBindState();
                    if (mState != Definition.BIND_UNKNOWN) {
                        onSuccess();
                    } else {
                        mTimes++;
                        Log.e(TAG, "unknown state");
                    }
                } else {
                    mTimes++;
                    Log.e(TAG, "bindState err:" + info);
                }
                if (mTimes >= BIND_STATE_MAX_RETRY_TIME) {
                    //TODO:zhujie
                    onError(new StateData(FirstConfigDef.DEBUG_ERR_CODE_CHECKING_100TIMES,
                            ResUtil.getString(R.string.debug_err_msg_checking_100times), generateErrorId(), null));
                }
            }
        }, 0, BIND_STATE_CHECKING_INTERVAL);
    }

    private BindState parseRobotBindInfo(String param) {
        BindState bindState = null;
        if (TextUtils.isEmpty(param)) {
            return null;
        }
        try {
            bindState = new Gson().fromJson(param, BindState.class);
        } catch (JsonSyntaxException | NullPointerException e) {
            e.printStackTrace();
        }
        return bindState;
    }
}
