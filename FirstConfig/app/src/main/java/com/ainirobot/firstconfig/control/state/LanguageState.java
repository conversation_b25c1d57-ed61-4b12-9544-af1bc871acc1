/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.PreStateBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.utils.SystemUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * 语言时区地区　设置　State
 */
public class LanguageState extends BaseState {
    private static final String TAG = LanguageState.class.getSimpleName();

    private volatile boolean isBack = false;

    LanguageState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
        if (data instanceof PreStateBean) {
            PreStateBean preBean = (PreStateBean) data;
            isBack = preBean.isBackPrePage();
        }
    }

    @Override
    protected void doAction() {
        Log.d(TAG, "doAction isBack :"+isBack);
        EventBus.getDefault().post(
                isBack ? new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_AREA_ZONE) :
                        new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_LANGUAGE),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

    @Override
    protected BaseState getNextState() {
        return StateEnum.CONFIGURE.getState();
    }

    @Override
    protected void exit() {
        super.exit();
    }

}
