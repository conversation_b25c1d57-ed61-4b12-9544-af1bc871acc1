package com.ainirobot.firstconfig.ui.view;

import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

public class MarkView extends View {

    private static final int PROGRESS_COLOR = Color.parseColor("#ffffff");
    private int mProgressColor = PROGRESS_COLOR;
    private int mProgressWidth = 15;
    private int mRadius = 100;

    private Paint progressPaint;

    private Status mStatus = Status.IDLE;
    private float lineValueLeft;//左边对勾
    private float lineValueRight;//右边对勾
    private AnimatorSet mAnimatorSet;

    public enum Status{
        IDLE,
        LoadSuccess,
    }

    public MarkView(Context context) {
        this(context,null);
    }

    public MarkView(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public MarkView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        //设置画笔
        setPaint();
    }

    private void setPaint() {
        progressPaint = new Paint();
        progressPaint.setAntiAlias(true);
        progressPaint.setDither(true);
        progressPaint.setColor(mProgressColor);
        progressPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        progressPaint.setStrokeWidth(mProgressWidth);
        progressPaint.setStrokeCap(Paint.Cap.ROUND);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if(mStatus == Status.LoadSuccess){
            canvas.save();
            canvas.translate(0,0);
            //画圆圈中的对勾
            canvas.drawLine(mRadius*2/3.0f,mRadius,mRadius*2/3.0f+lineValueLeft,mRadius+lineValueLeft,progressPaint);
            canvas.drawLine(mRadius,mRadius+mRadius/3.0f,mRadius+1.3f*lineValueRight,mRadius+mRadius/3.0f-1.5f*lineValueRight,progressPaint);
            canvas.restore();
        }
    }

    public void startAnima(){
        //对勾左边线的属性动画
        ValueAnimator animatorLeft = ValueAnimator.ofFloat(0f,mRadius/3f);
        animatorLeft.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                lineValueLeft = (float) animation.getAnimatedValue();
                invalidate();//重绘，调onDraw()重绘
            }
        });
        //对勾右边线的属性动画
        ValueAnimator animatorRight = ValueAnimator.ofFloat(0f,mRadius/2f);
        animatorRight.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                lineValueRight = (float) animation.getAnimatedValue();
                invalidate();
            }
        });
        //将多个动画组合到一起需要借助AnimatorSet这个类
        mAnimatorSet = new AnimatorSet();
        mAnimatorSet.play(animatorRight).after(animatorLeft);
        mAnimatorSet.setDuration(250);
        mAnimatorSet.start();
    }

    public Status getStatus() {
        return mStatus;
    }

    public void setStatus(Status mStatus) {
        this.mStatus = mStatus;
        invalidate();
    }

    public void stopAllAnimate(){
        if (mAnimatorSet == null){
            return;
        }
        if (mAnimatorSet.isRunning()){
            mAnimatorSet.end();
            mAnimatorSet = null;
        }
    }

}