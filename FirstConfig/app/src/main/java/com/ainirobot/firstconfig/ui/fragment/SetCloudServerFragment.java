package com.ainirobot.firstconfig.ui.fragment;


import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.CloudServerBean;
import com.ainirobot.firstconfig.bean.TimeZoneBean;
import com.ainirobot.firstconfig.databinding.CloudServerBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.CloudServerViewModel;
import com.ainirobot.firstconfig.utils.ResUtil;

import java.util.ArrayList;
import java.util.List;


/**
 * 只有海外项目才会用到.
 * 配置云服务地址
 */
public class SetCloudServerFragment extends BaseFragment<CloudServerViewModel, CloudServerBinding> {

    private static final String TAG = SetCloudServerFragment.class.getSimpleName();
    private MyAdapter mAdapter;


    @Override
    protected int getLayoutID() {
        return R.layout.fragment_cloud_server;
    }

    @Override
    protected void initData() {

        mBinding.setClickListener(this);
        mAdapter = new MyAdapter(getActivity());
        mBinding.lvServers.setAdapter(mAdapter);
        mBinding.lvServers.setOnItemClickListener((parent, view, position, id) -> {
            CloudServerBean cloudServerBean = (CloudServerBean) mAdapter.getItem(position);
            String serverId = cloudServerBean.getServer_id();
            mViewModel.updateCloudServer(serverId);
            itemClick(position);
        });

        mViewModel.getState().observe(this, cloudServerBeans -> {
            mAdapter.updateData(cloudServerBeans);
            mAdapter.notifyDataSetChanged();
        });

        mViewModel.getNextState().observe(this, Boolean -> {
            mBinding.buttNext.setText(Boolean ? ResUtil.getString(R.string.cloud_reboot) : ResUtil.getString(R.string.next));
        });

    }


    private void itemClick(int clickPosition) {
        int count = mAdapter.getCount();
        for (int i = 0; i < count; i++) {
            ((CloudServerBean) mAdapter.getItem(i)).setDefault(i == clickPosition);
        }
        mAdapter.notifyDataSetChanged();
    }

    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<CloudServerBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<CloudServerBean> getData() {
            return mData;
        }

        public void updateData(List<CloudServerBean> list) {
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            MyAdapter.ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvLanguage = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            CloudServerBean bean = (CloudServerBean) mData.get(position);
            holder.tvLanguage.setText(bean.getServer_name());
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.select : R.drawable.unselect);
            return convertView;
        }

        class ViewHolder {
            TextView tvLanguage;
            ImageView imSelect;
        }
    }


}
