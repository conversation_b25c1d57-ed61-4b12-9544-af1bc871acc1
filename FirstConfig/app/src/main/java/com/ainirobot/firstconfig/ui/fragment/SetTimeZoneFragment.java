package com.ainirobot.firstconfig.ui.fragment;

import android.content.Context;
import android.util.Log;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.TimeZoneBean;
import com.ainirobot.firstconfig.databinding.TimeZoneBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.TimeZoneViewModel;

import java.util.ArrayList;
import java.util.List;

import androidx.lifecycle.Observer;

/**
 * 配置时区
 */
public class SetTimeZoneFragment extends BaseFragment<TimeZoneViewModel, TimeZoneBinding> {

    private static final String TAG = SetTimeZoneFragment.class.getSimpleName();

    private MyAdapter mAdapter;


    @Override
    protected int getLayoutID() {
        return R.layout.fragment_time_zone;
    }

    @Override
    protected void initData() {
        mViewModel.getTimeZoneState().observe(this, timeZoneBeans -> {
            List<TimeZoneBean> zoneBeans = handleRepeatDefaultBean(timeZoneBeans);
            mAdapter.updateData(zoneBeans);
            mAdapter.notifyDataSetChanged();
            smoothToDefault();
        });

        mBinding.setClickListener(this);
        mAdapter = new MyAdapter(getActivity());
        mBinding.lvTimeZone.setAdapter(mAdapter);
        mBinding.lvTimeZone.setOnItemClickListener((parent, view, position, id) -> {
            TimeZoneBean timeZoneBean = (TimeZoneBean) mAdapter.getItem(position);
            Log.e(TAG, "timeZoneBean = " + timeZoneBean);
            mViewModel.updateTimeCode(timeZoneBean.getTimeZoneCode());
            selectPosition(position);
        });

        mViewModel.getGmtState().observe(this, timeZoneName -> {
            smoothToTimeZone(timeZoneName);
            updateQuickUi();
        });

        mViewModel.getCurrentCacheGmtState().observe(this, integer -> {
            updateQuickUi();
        });

        mBinding.lvTimeZone.setOnScrollListener(new AbsListView.OnScrollListener() {

            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                switch (scrollState) {
                    case AbsListView.OnScrollListener.SCROLL_STATE_FLING:
                        Log.e(TAG, "SCROLL_STATE_FLING");
                        break;
                    case AbsListView.OnScrollListener.SCROLL_STATE_IDLE:
                        handleScrollStateIdle(view);
                        break;
                    case AbsListView.OnScrollListener.SCROLL_STATE_TOUCH_SCROLL:
                        Log.e(TAG, "SCROLL_STATE_TOUCH_SCROLL");
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem,
                                 int visibleItemCount, int totalItemCount) {

            }
        });
    }

    private void updateQuickUi() {
        mBinding.nextGmt.setAlpha((mViewModel.getCurrentCacheGmt() == mViewModel.GMT_MAX ? 0.3f : 1.0f));
        mBinding.preGmt.setAlpha((mViewModel.getCurrentCacheGmt() == mViewModel.GMT_MIN ? 0.3f : 1.0f));
    }

    /**
     * 滑动停止时，保证第一个Item 显示完整itemView ，而不是显示一半或者不完全。
     *
     * @param view
     */
    private void handleScrollStateIdle(AbsListView view) {
        int firstPostion = view.getFirstVisiblePosition();
        int lastVisiblePosition = view.getLastVisiblePosition();
        Log.e(TAG, "SCROLL_STATE_IDLE firstPosition : " + firstPostion + ", lastVisiblePosition :" + lastVisiblePosition);
        View firstChild = view.getChildAt(0);
        final int height = firstChild.getHeight();
        final int top = -(firstChild.getTop());
        if (top > (height / 2)) {
            view.post(() -> view.smoothScrollBy(height - top, 200));
        } else {
            view.post(() -> view.smoothScrollBy(-top, 200));
        }
        Log.e(TAG, "top = " + top + ", height = " + height);
    }

    private void smoothToTimeZone(String timeZoneName) {
        Log.d(TAG, "smooth quick : " + timeZoneName);
        List<TimeZoneBean> dataList = mAdapter.getData();
        for (int i = 0; i < dataList.size(); i++) {
            if (((TimeZoneBean) mAdapter.getItem(i)).getTimeZoneName().equals(timeZoneName)) {
                Log.d(TAG, "smoothToTimeZone i : " + i);
                int mQuickPosition = i;
//                mBinding.lvTimeZone.smoothScrollToPosition(mQuickPosition); // listView 的最后一行显示mQuickPosition位置的item
                mBinding.lvTimeZone.post(() -> {
                    mBinding.lvTimeZone.setSelection(mQuickPosition); // listView 首行显示mQuickPosition位置的item
                });
                break;
            }
        }
    }

    /**
     * 韩语默认的时区id存在相同的,获取最后一个作为默认
     *
     * @param timeZoneBeans
     * @return
     */
    private List<TimeZoneBean> handleRepeatDefaultBean(List<TimeZoneBean> timeZoneBeans) {
        int lastDefaultIndex = 0;
        SparseArray<TimeZoneBean> sparseArray = new SparseArray<>();
        for (int i = 0; i < timeZoneBeans.size(); i++) {
            TimeZoneBean bean = timeZoneBeans.get(i);
            if (bean.isDefault()) {
                sparseArray.put(i, timeZoneBeans.get(i));
                if (sparseArray.size() > 1) {
                    lastDefaultIndex = i;
                    Log.d(TAG, "lastDefaultIndex: " + lastDefaultIndex);
                }
            }
        }
        if (sparseArray.size() > 1) {
            for (int i = 0; i < timeZoneBeans.size(); i++) {
                timeZoneBeans.get(i).setDefault(i == lastDefaultIndex);
            }
        }
        sparseArray.clear();
        return timeZoneBeans;
    }

    private void smoothToDefault() {
        List<TimeZoneBean> dataList = mAdapter.getData();
        for (TimeZoneBean bean : dataList) {
            if (bean.isDefault()) {
                int i = dataList.indexOf(bean);
                Log.d(TAG, "smoothToDefault dest :" + i);
                mBinding.lvTimeZone.setSelection(i);
            }
        }
    }

    private void selectPosition(int clickPosition) {
        int count = mAdapter.getCount();
        for (int i = 0; i < count; i++) {
            ((TimeZoneBean) mAdapter.getItem(i)).setDefault(i == clickPosition);
        }
        mAdapter.notifyDataSetChanged();
    }


    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<TimeZoneBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<TimeZoneBean> getData() {
            return mData;
        }

        public void updateData(List<TimeZoneBean> list) {
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvLanguage = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            TimeZoneBean bean = (TimeZoneBean) mData.get(position);
            holder.tvLanguage.setText(bean.getTimeZoneName());
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.select : R.drawable.unselect);

//            if (mTag != 2 && mLanguageShown.equals(mData.get(position))) {
//                holder.imSelect.setImageResource(R.drawable.select);
//            } else if (mTag == 2 && mLanguagePostion == position) {
//                holder.imSelect.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.select));
//            } else {
//                holder.imSelect.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.unselect));
//            }

            return convertView;
        }

//        public void setTag(int tag, int postion) {
//            mLanguagePostion = postion;
//            mTag = tag;
//        }

        class ViewHolder {
            TextView tvLanguage;
            ImageView imSelect;
        }
    }


}
