package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.exception.InvalidArgumentException;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;

import org.simple.eventbus.EventBus;

import java.util.regex.Pattern;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

public class BindSuccessViewModel extends BaseViewModel<BaseModel> {


    private MutableLiveData<String> mRobotNameUpdateState = new MutableLiveData<>();

    public BindSuccessViewModel(@NonNull Application application) {
        super(application);
    }

    public MutableLiveData<String> getRobotNameState() {
        return mRobotNameUpdateState;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        try {
            RobotSettings.registerRobotSettingListener(getApplication().getApplicationContext()
                    , mSettingListener, RobotSettings.SETTINGS_GLOBAL_ROBOT_NAME);
        } catch (InvalidArgumentException e) {
            e.printStackTrace();
        }
        //检查机器人名称语言是否和服务端主语言一致
        checkRobotName();
    }

    private RobotSettingListener mSettingListener = new RobotSettingListener() {
        @Override
        public void onRobotSettingChanged(String key) {
            super.onRobotSettingChanged(key);
            if (RobotSettings.SETTINGS_GLOBAL_ROBOT_NAME.equals(key)) {
                String robotName = RobotSettings.getRobotName(getApplication().getApplicationContext());
                Log.d(TAG, "onRobotSettingChanged robotName = "+robotName);
                mRobotNameUpdateState.postValue(robotName);
            }
        }
    };

    /**
     * 由于机器人的名字,绑定的时候服务端返回的,但是后续请求企业所持的主语言
     * 可能和机器人的名称的语言不一致.所以首次配置流程的机器人的名称也要切换到主语言.
     * 中文环境使用中文名称, 其他环境均使用英文名称.
     */
    private void checkRobotName() {

        String robotName = RobotSettings.getRobotName(FirstConfigApplication.getAppContext());
        boolean isNameZh = Pattern.compile("[\\u4e00-\\u9fa5]").matcher(robotName).find();

        String mainLangCode = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);//服务端主语言已经设置完
        Log.d(TAG, "mainLangCode = " + mainLangCode + ", robotName :" + robotName+", isNameZh :"+isNameZh);
        boolean nameSameToLangCode = (mainLangCode.startsWith("zh") && isNameZh) || (!mainLangCode.startsWith("zh") && !isNameZh);
        if (nameSameToLangCode) {
            Log.d(TAG, "RobotName's language same to server main LangCode ");
            return;
        }

        String robotNumber = parseRobotNumber(robotName);
        if (TextUtils.isEmpty(robotNumber)) {
            return;
        }

        String newName = FirstConfigApplication.getAppContext().getResources().getString(
                mainLangCode.startsWith("zh") ? R.string.robot_name_zh : R.string.robot_name_en,
                robotNumber);

        Log.d(TAG, "modifyRobotName newName : "+newName);
        RobotSettings.storage2SystemSettings(FirstConfigApplication.getAppContext(),
                RobotSettings.SETTINGS_GLOBAL_ROBOT_NAME, newName);

        new Thread(() -> requestModifyRobotName(newName)).start();
    }

    private String parseRobotNumber(String robotName) {
        if (TextUtils.isEmpty(robotName)) {
            Log.e(TAG, "parseRobotNumber robotName is empty !");
            return "";
        }

        String number = Pattern.compile("[^0-9]").matcher(robotName).replaceAll("");
        Log.d(TAG, "parseRobotNumber: " + number);
        return number;
    }


    private void requestModifyRobotName(String newName) {
        SystemApi.getInstance().modifyRobotName(0, newName, new CommandListener() {

            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "onResult result :" + result + " , message :" + message + " ,extraData :" + extraData);
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
                Log.d(TAG, "onError errorCode :" + errorCode + " , errorString :" + errorString + " ,extraData :" + extraData);
            }

            @Override
            public void onStatusUpdate(int status, String data, String extraData) {
                super.onStatusUpdate(status, data, extraData);
                Log.d(TAG, "onStatusUpdate status :" + status + ", data :" + data + ", extraData :" + extraData);
            }
        });
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.bottomBtn:
                endFirstConfig(Definition.FIRST_CONFIG_END_TO_NEW_MAP);
                break;
            case R.id.bottomBtnDes:
                endFirstConfig(Definition.FIRST_CONFIG_END_TO_SYNC_CLOUD_MAP);
                break;
            default:
                break;
        }
    }

    public void endFirstConfig(int type) {
        Log.d(TAG, "endFirstConfig type ------- " + type);//先设置Home LauncherActivity 再finish自己
        EventBus.getDefault().post(type, FirstConfigDef.EVENTBUS_TAG_FIRST_CONFIG_FINISH);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            RobotSettings.unRegisterRobotSettingListener(getApplication().getApplicationContext(), mSettingListener);
        } catch (InvalidArgumentException e) {
            e.printStackTrace();
        }
    }
}
