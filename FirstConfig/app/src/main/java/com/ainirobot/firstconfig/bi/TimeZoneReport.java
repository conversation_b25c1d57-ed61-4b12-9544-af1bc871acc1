package com.ainirobot.firstconfig.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class TimeZoneReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_timezone";
    private static final String CTIME = "ctime";
    private static final String TIME_ZONE = "timezone";
    private static final String WAY = "way";

    public TimeZoneReport() {
        super(TABLE_NAME);
    }

    public TimeZoneReport addTimeZone(String timeZoneCode) {
        addData(TIME_ZONE, timeZoneCode);
        return this;
    }

    public TimeZoneReport addWay() {
        addData(WAY, 1);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
