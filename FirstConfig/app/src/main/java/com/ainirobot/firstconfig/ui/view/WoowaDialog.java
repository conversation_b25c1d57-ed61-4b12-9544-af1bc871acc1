/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.firstconfig.ui.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Outline;
import android.os.Bundle;
import android.text.Html;
import android.util.Log;
import android.view.View;
import android.view.ViewOutlineProvider;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.CloudServerBean;
import com.ainirobot.firstconfig.utils.ResUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.StringDef;
import androidx.appcompat.app.AlertDialog;
import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * 韩国时序时提示，重启后才会开启动态密码功能．
 */
public class WoowaDialog extends AlertDialog implements View.OnClickListener{
    private static final String TAG = WoowaDialog.class.getSimpleName();

    private Context mContext;
    private TextView mConfirm, mCancel;
    private ConstraintLayout mLayout;
    private TextView mContent;
    private List<CloudServerBean> mList = new ArrayList<>();
    private boolean serverSwitched = false;
    private TextView mTitle;


    public WoowaDialog(Context context) {
        this(context,0);
        mContext = context;
    }

    public WoowaDialog(Context context, int theme) {
        super(context, theme);
        mContext = context;
    }

    private ClickListener mClickListener = null;
    public interface ClickListener{
        void onConfirmClick();
        void onCancelClick();
    }

    public WoowaDialog setDialogClickListener(ClickListener listener){
        this.mClickListener = listener;
        return this;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_woowa_dialog);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 980;
        p.height = 650;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);
        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_parent);
        mTitle = (TextView)findViewById(R.id.title);
        mContent = (TextView)findViewById(R.id.content);
        mConfirm = (TextView) findViewById(R.id.confirm);
    }


    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout,"alpha",0,1)
                .setDuration(170)
                .start();
        super.show();

        mConfirm.setOnClickListener(this);
        Log.d(TAG, "show Woowa Dialog");
    }

    public void setContent(String content){
        mContent.setText(Html.fromHtml(content));
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId){
            case R.id.confirm:
                if (mClickListener != null) {
                    mClickListener.onConfirmClick();
                }
                dismiss();
                break;
            case R.id.cancel:
                dismiss();
                break;
            default:
                Log.d(TAG, "default case");
                break;

        }
    }

    /**
     * Timezone常量值
     */
    public interface TimeZoneConstant {

        @StringDef({Timezone.BEIJING, Timezone.SEOUL})
        @Retention(RetentionPolicy.SOURCE)
        @interface Timezone {
            String BEIJING = "Asia/Shanghai";
            String SEOUL = "Asia/Seoul";
        }
    }
}
