package com.ainirobot.firstconfig.control;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigApplication;
import java.util.concurrent.CopyOnWriteArrayList;

public class SystemStatusManager {

    private static final String TAG = SystemStatusManager.class.getSimpleName();
    private boolean mIsCharging = false;
    private PowerReceiver mPowerReceiver = null;
    private CopyOnWriteArrayList<IBatteryChangeListener> mBatteryChangeListeners = null;

    private static class SingletonHolder {
        private static final SystemStatusManager mInstance = new SystemStatusManager();
    }

    public static SystemStatusManager getInstance() {
        return SystemStatusManager.SingletonHolder.mInstance;
    }

    private SystemStatusManager(){
        mBatteryChangeListeners = new CopyOnWriteArrayList<>();
    }

    public interface IBatteryChangeListener{
        void onBatteryChange(boolean isCharging);
    }
    public void addBatteryChangeListener(IBatteryChangeListener  listener){
        if (null == listener) {
            Log.e(TAG, "attempt to add a null listener!");
            return;
        }
        if (mBatteryChangeListeners.contains(listener)) {
            Log.e(TAG, "attempt to add same listener!");
            return;
        }
        mBatteryChangeListeners.add(listener);
    }
    public void removeBatteryChangeListener(IBatteryChangeListener listener){
        if (null == listener) {
            Log.e(TAG, "attempt to remove a null listener!");
            return;
        }
        mBatteryChangeListeners.remove(listener);
    }

    public void registerPowerReceiver() {
        Log.d(TAG,"registerPowerReceiver ... ");
        IntentFilter powerFilter = new IntentFilter();
        powerFilter.addAction(Intent.ACTION_BATTERY_CHANGED);
        mPowerReceiver = new PowerReceiver();
        FirstConfigApplication.getAppContext().registerReceiver(mPowerReceiver, powerFilter);
    }

    public void unRegisterPowerReceiver() {
        if (mPowerReceiver != null) {
            Log.d(TAG,"unRegisterPowerReceiver  ... ");
            FirstConfigApplication.getAppContext().unregisterReceiver(mPowerReceiver);
            mPowerReceiver = null;
        }
    }

    public class PowerReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null) {
                return;
            }
            int charging = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
            boolean isCharging = charging != 0;
            if (mIsCharging != isCharging){
                mIsCharging = isCharging;
                for (IBatteryChangeListener listener : mBatteryChangeListeners) {
                    listener.onBatteryChange(mIsCharging);
                }
            }
        }
    }


}
