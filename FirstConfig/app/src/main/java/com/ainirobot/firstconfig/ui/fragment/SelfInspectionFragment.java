/*
 *
 *  *  Copyright (C) 2017 OrionStar Technology Project
 *  *
 *  *  Licensed under the Apache License, Version 2.0 (the "License");
 *  *  you may not use this file except in compliance with the License.
 *  *  You may obtain a copy of the License at
 *  *
 *  *       http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  *  Unless required by applicable law or agreed to in writing, software
 *  *  distributed under the License is distributed on an "AS IS" BASIS,
 *  *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  *  See the License for the specific language governing permissions and
 *  *  limitations under the License.
 *
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.databinding.SelfInspectBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.SelfInspectViewModel;

import androidx.annotation.Nullable;

public class SelfInspectionFragment extends BaseFragment<SelfInspectViewModel, SelfInspectBinding> {

    private AnimationDrawable animationDrawable;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_self_inspection;
    }

    @Override
    protected void initData() {
        if (isViewBound()){
            Log.d(TAG, "initData ");
            animationDrawable = (AnimationDrawable) getActivity().getResources().getDrawable(R.drawable.anim_inspect_loading);
            mBinding.inspectingLoading.setImageDrawable(animationDrawable);
            mBinding.inspectingImage.setImageResource(R.drawable.inspect_title);
        }
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (animationDrawable != null) {
            Log.d(TAG, "onViewCreated animationDrawable start");
            animationDrawable.start();
        }
    }
}
