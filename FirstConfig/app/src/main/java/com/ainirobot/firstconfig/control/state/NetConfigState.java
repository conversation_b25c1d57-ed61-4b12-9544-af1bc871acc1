/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.PreStateBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.utils.SystemUtils;
import com.ainirobot.firstconfig.utils.WifiUtils;

import org.simple.eventbus.EventBus;

/**
 * 监听网络状态连接
 */
public class NetConfigState extends BaseState {
    private static final String TAG = NetConfigState.class.getSimpleName();

    private ConnectivityManager mConnectivityManager;

    private ConnectivityManager.NetworkCallback mNetworkCallback = new ConnectivityManager.NetworkCallback() {
        @Override
        public void onAvailable(Network network) {
            super.onAvailable(network);

            Log.d(TAG, "onAvailable called ");
        }
    };


    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {

            if (intent == null || TextUtils.isEmpty(intent.getAction())) {
                return;
            }
            Log.d(TAG, "onReceive action :" + intent.getAction());
            if (FirstConfigDef.FIRSTCONFIG_ACTION_WIFI_NEXT.equals(intent.getAction())) {
                handleNext();
            } else if (FirstConfigDef.FIRSTCONFIG_ACTION_WIFI_BACK.equals(intent.getAction())) {
                handleBack();
            }
        }
    };

    private void handleNext() {
        //通知界面进行后台登录
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_NET_OK),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
        // 网络正常后,立即进行后台登录和企业绑定
        onSuccess();
    }

    private void handleBack() {
        EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_BACK_PRE_PAGE);
    }

    NetConfigState(int resId) {
        super(resId);
        mConnectivityManager = (ConnectivityManager) mContext
                .getSystemService(Context.CONNECTIVITY_SERVICE);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        requestNetwork();
        // 网络不正常, 启动RobotSettings Wifi
        Log.d(TAG, "NetConfigState check ");
        EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_CONFIG_NET);
        IntentFilter filter = new IntentFilter();
        filter.addAction(FirstConfigDef.FIRSTCONFIG_ACTION_WIFI_BACK);
        filter.addAction(FirstConfigDef.FIRSTCONFIG_ACTION_WIFI_NEXT);
        mContext.registerReceiver(mReceiver, filter);
    }


    @Override
    protected void exit() {
        mConnectivityManager.unregisterNetworkCallback(mNetworkCallback);
        if (mReceiver != null) {
            Log.d(TAG, "exit unRegister Receiver");
            mContext.unregisterReceiver(mReceiver);
        }
        super.exit();
    }

    @Override
    protected BaseState getNextState() {
        return StateEnum.CHECKING.getState();
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.LANGUAGE.getState();
    }

    @Override
    protected boolean isAvailable() {
        return true;
    }

    private void requestNetwork() {
        mConnectivityManager.requestNetwork(
                new NetworkRequest
                        .Builder()
                        .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                        .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
                        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                        .addTransportType(NetworkCapabilities.TRANSPORT_ETHERNET)
                        .build(),
                mNetworkCallback);
    }
}
