
package com.ainirobot.firstconfig.control.state;

import android.content.Intent;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.QrCodeBean;
import com.ainirobot.firstconfig.bean.ServerLanguageBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;

import org.simple.eventbus.EventBus;

import java.util.List;

/**
 * 通知云端切换系统
 */
public class ReportOSTypeState extends BaseState {
    private static final String TAG = ReportOSTypeState.class.getSimpleName();
    private Object mData;

    ReportOSTypeState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        if (ProductInfo.isMeissa2() || ProductInfo.isMiniProduct()) {
            String OSType = SharedPrefUtil.getInstance().getOSType();
            String settingsOSType = RobotSettings.getGlobalSettings(mContext, Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, "");
            Log.d(TAG, "doAction OSType :" + OSType + " , settingsOSType :" + settingsOSType);

            if (TextUtils.equals(OSType, settingsOSType)) {
                onSuccess();
            } else {
                requestSwitchOSType();
            }
        } else {
            onSuccess();
        }
    }

    @Override
    protected BaseState getNextState() {
        return StateEnum.SERVERLANG.getState();
    }

    @Override
    protected void exit() {
        super.exit();
    }

    private void requestSwitchOSType() {
        String OSType = SharedPrefUtil.getInstance().getOSType();
        SystemApi.getInstance().remoteReportOSType(Definition.MODULE_REQ_ID, OSType, new CommandListener() {

            @Override
            public void onResult(int result, String message, String extraData) {
                super.onResult(result, message, extraData);
                Log.d(TAG, "remoteReportOSType onResult result :" + result + " , message :" + message + " ,extraData :" + extraData);
                if (result == Definition.RESULT_OK && !TextUtils.isEmpty(message) &&
                        !"timeout".equals(message) && !Definition.FAILED.equals(message)) {
                    String OSType = SharedPrefUtil.getInstance().getOSType();
                    RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE, OSType);
                    reboot();
                }
            }

            @Override
            public void onError(int errorCode, String errorString, String extraData) throws RemoteException {
                super.onError(errorCode, errorString, extraData);
                Log.d(TAG, "remoteReportOSType onError errorCode :" + errorCode + " , errorString :" + errorString + " ,extraData :" + extraData);
            }

            @Override
            public void onStatusUpdate(int status, String data, String extraData) {
                super.onStatusUpdate(status, data, extraData);
                Log.d(TAG, "remoteReportOSType onStatusUpdate status :" + status + ", data :" + data + ", extraData :" + extraData);
            }
        });
    }

    private void reboot(){
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
    }
}
