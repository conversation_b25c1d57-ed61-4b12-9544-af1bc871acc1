/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.bean;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.firstconfig.utils.SystemUtils;

public enum ZoneEnum {
    ZH_CN("zh_CN", "CN", "Asia/Shanghai"),
    EN_US("en_US", ProductInfo.isMiniProduct() ? "US" : "SG",
            ProductInfo.isMiniProduct() ? "America/New_York" : "Asia/Singapore"),
    JA_JP("ja_<PERSON>", "<PERSON>", "Asia/Tokyo"),
    KO_KR("ko_KR", "KR", "Asia/Seoul"),
    TH_TH("th_TH", "TH", "Asia/Bangkok"),
    ES_ES("es_ES", "ES", "Europe/Madrid"),
    DE_DE("de_DE", "DE", "Europe/Berlin"),
    ZH_GD("zh_GD", "CN", "Asia/Hong_Kong"),
    UNDEFINED( ProductInfo.isMiniProduct() || ProductInfo.isOverSea() ? "en_US" : "zh_CN",
            ProductInfo.isMiniProduct() || ProductInfo.isOverSea() ? "US" : "CN",
            ProductInfo.isMiniProduct() || ProductInfo.isOverSea() ? "America/New_York" : "Asia/Shanghai");

    private static String TAG = ZoneEnum.class.getSimpleName();
    public final String codeName;
    public final String areaZone;
    public final String timeZone;

    ZoneEnum(String codeName, String areaZone, String timeZone) {
        this.codeName = codeName;
        this.areaZone = areaZone;
        this.timeZone = timeZone;
    }

    public static ZoneEnum getZoneEnumByCodeName(String codeName) {
        if (TextUtils.isEmpty(codeName)) {
            Log.e(TAG, "codeName: " + codeName);
            return ZoneEnum.UNDEFINED;
        }
        for (ZoneEnum lang : ZoneEnum.values()) {
            if (codeName.equals(lang.codeName)) {
                return lang;
            }
        }
        return ZoneEnum.UNDEFINED;
    }
}
