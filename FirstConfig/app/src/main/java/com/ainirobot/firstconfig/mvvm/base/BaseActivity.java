package com.ainirobot.firstconfig.mvvm.base;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.mvvm.util.ReflexUtil;

import java.util.LinkedList;

import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelProvider;

/**
 *
 * @param <VM>
 * @param <B>
 */
public abstract class BaseActivity<VM extends BaseViewModel<BaseModel> , B extends ViewDataBinding>
        extends AppCompatActivity {

    protected final String TAG = FirstConfigDef.FLAG + getClass().getSimpleName();
    protected VM mViewModel;
    protected B mBinding;
    private Intent intent;
    private final LinkedList<BaseFragment> fragmentStack = new LinkedList<>();


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //初始化binging
        mBinding = DataBindingUtil.setContentView(this, getContentViewId());
        //初始化mViewModel
        initViewModel();

        initView();
        initData();
    }

    private void initViewModel() {
        ReflexUtil<VM> reflexUtils = new ReflexUtil<>();
        Class<VM> clz = reflexUtils.getParameterizedClz(getClass());
        if (clz != null) {
            mViewModel = new ViewModelProvider(this).get(clz);
        } else {
            mViewModel = (VM) new ViewModelProvider(this).get(BaseViewModel.class);
        }
        getLifecycle().addObserver(mViewModel);
        Log.d(TAG, "initViewModel: "+mViewModel.getClass());
    }


    /**
     * 获取当前activity布局文件,并初始化binding
     * @return
     */
    protected abstract @LayoutRes int getContentViewId();

    protected abstract void initView();

    protected abstract void initData();

    protected void replace(BaseFragment fragment, @IdRes int id, String tag, boolean isAddBackStack) {
        Log.i(TAG, "new_tag:" + tag + " isAddBackStack:" + isAddBackStack);
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(id, fragment, tag);
        if (isAddBackStack) {
            fragmentStack.add(fragment);
            Log.i(TAG, "replace add back stack:" + tag+", size :"+fragmentStack.size());
        }
        transaction.commit();
    }

    /**
     * 获取上一次缓存的BaseFragment 子类的实例Fragment
     * @return
     */
    protected BaseFragment getLastCacheFragment(){
        Log.d(TAG, "getLastCacheFragment fragmentStack :"+fragmentStack);
        if (fragmentStack.size() > 0 ){
            return fragmentStack.pop();
        }else {
            return null;
        }
    }

    protected void clearBackStack() {
        Log.i(TAG, "clearBackStack: " + fragmentStack);
        fragmentStack.clear();
    }

    protected void replace(BaseFragment fragment) {
        replace(fragment, R.id.container, fragment.getClass().getSimpleName(), false);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Log.i(TAG, "onKeyDown: keyCode:" + keyCode + " event:" + event);

        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN || keyCode== KeyEvent.KEYCODE_VOLUME_UP) {
            return false;
        }

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            Log.i(TAG, "onKeyDown: fragment stack size:" + fragmentStack.size());
            if (fragmentStack.size() > 0) {
                BaseFragment fragment = fragmentStack.pop();
                replace(fragment);
            } else {
                finish();
            }
        }
        return true;
    }

}
