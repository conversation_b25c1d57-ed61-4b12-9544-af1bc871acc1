/*
 * Copyright (C) 2017 OrionStar Technology Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ainirobot.firstconfig.bean;

import java.io.File;

/**
 * map file bean
 */
public class MapBean {
    // map name
    private String fileName;
    // map path
    private String filePath;
    // pgm.zip path
    private String mapFilePath;
    // data.zip path
    private String dataFilePath;


    public MapBean(String fileName, String filePath, String mapFilePath, String dataFilePath) {
        this.fileName = fileName;
        this.filePath = filePath;
        this.mapFilePath = mapFilePath;
        this.dataFilePath = dataFilePath;
    }

    public MapBean() {
    }

    public MapBean(File file) {
        this.set(file);
    }

    public void set(File file) {
        this.fileName = file.getName();
        this.filePath = file.getAbsolutePath();
        this.mapFilePath = file.getAbsolutePath() + File.separator + "pgm.zip";
        this.dataFilePath = file.getAbsolutePath() + File.separator + "data.zip";
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getMapFilePath() {
        return mapFilePath;
    }

    public void setMapFilePath(String mapFilePath) {
        this.mapFilePath = mapFilePath;
    }

    public String getDataFilePath() {
        return dataFilePath;
    }

    public void setDataFilePath(String dataFilePath) {
        this.dataFilePath = dataFilePath;
    }

    @Override
    public String toString() {
        return "MapBean{" +
                "fileName='" + fileName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", mapFilePath='" + mapFilePath + '\'' +
                ", dataFilePath='" + dataFilePath + '\'' +
                '}';
    }
}