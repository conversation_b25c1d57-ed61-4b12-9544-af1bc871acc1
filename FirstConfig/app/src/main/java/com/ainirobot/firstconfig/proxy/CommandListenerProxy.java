package com.ainirobot.firstconfig.proxy;

import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.client.listener.CommandListener;

/**
 *　用户处理SystemApi命令执行结果的监听
 */
public class CommandListenerProxy extends CommandListener implements ICommandListener {

    private ICommandListener mCommandListener;
    public CommandListenerProxy(ICommandListener listener) {
        this.mCommandListener = listener;
    }

    public CommandListenerProxy() {
    }

    @Override
    public void onResult(int result, String message) {
        Log.d("CommandListenerProxy", "result=" + result + ",message=" + message);
        if (mCommandListener != null) {
            mCommandListener.onResult(result, message);
        }
    }

    @Override
    public void onStatusUpdate(int status, String data) {
        if (mCommandListener != null) {
            mCommandListener.onStatusUpdate(status, data);
        }
    }

    @Override
    public void onError(int errorCode, String errorString) throws RemoteException {
        if (mCommandListener != null) {
            mCommandListener.onError(errorCode, errorString);
        }
    }
}
