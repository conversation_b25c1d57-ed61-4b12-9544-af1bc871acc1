package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.util.Log;


import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;
import org.simple.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * Feature:处理主业务
 */
public class MainViewModel extends BaseViewModel<BaseModel> {


    private MutableLiveData<UIMessage> fragmentData = new MutableLiveData<>();
    private MutableLiveData<Boolean> netData = new MutableLiveData<>();
    private MutableLiveData<Boolean> finishState = new MutableLiveData<>();

    public MainViewModel(@NonNull Application application) {
        super(application);

    }

    public MutableLiveData<UIMessage> getFragmentState() {
        return fragmentData;
    }

    public MutableLiveData<Boolean> getNetData() {
        return netData;
    }

    public MutableLiveData<Boolean> getFinishState(){
        return finishState;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT)
    private void handleFragmentData(@NonNull UIMessage msg) {
        Log.d(TAG, "handle Fragment data, " + msg.toString());
        fragmentData.postValue(msg);
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_CONFIG_NET)
    private void onEventBus_configNet(boolean status) {
        Log.d(TAG, "onEventBus_configNet status:" + status);// 进行后续的联网.
        netData.postValue(status);
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_FINISH_ACTIVITY)
    private void finishActivity(boolean status) {
        Log.d(TAG, "finishActivity status:" + status);// 等设置Home LauncherActivity后,再finish
        finishState.postValue(status);
    }




    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }


    protected void onFinish() {
        Log.d(TAG, "onFinish ");
    }

}
