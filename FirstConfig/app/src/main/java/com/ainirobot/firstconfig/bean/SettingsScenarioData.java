package com.ainirobot.firstconfig.bean;

import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;

public class SettingsScenarioData {

    @SerializedName("settings_scenario_id")
    private String settingsScenarioId;
    @SerializedName("settings_scenario")
    private SettingsScenarioBean settingsScenario;

    public String getSettingsScenarioId() {
        return settingsScenarioId;
    }

    public void setSettingsScenarioId(String settingsScenarioId) {
        this.settingsScenarioId = settingsScenarioId;
    }

    public SettingsScenarioBean getSettingsScenario() {
        return settingsScenario;
    }

    public void setSettingsScenario(SettingsScenarioBean settingsScenario) {
        this.settingsScenario = settingsScenario;
    }

    public static class SettingsScenarioBean {
        @SerializedName("settings_scenario_id")
        private String settingsScenarioId;
        @SerializedName("scenario_name")
        private String scenarioName;
        @SerializedName("settings")
        private SettingsBean settings;

        public String getSettingsScenarioId() {
            return settingsScenarioId;
        }

        public void setSettingsScenarioId(String settingsScenarioId) {
            this.settingsScenarioId = settingsScenarioId;
        }

        public String getScenarioName() {
            return scenarioName;
        }

        public void setScenarioName(String scenarioName) {
            this.scenarioName = scenarioName;
        }

        public SettingsBean getSettings() {
            return settings;
        }

        public void setSettings(SettingsBean settings) {
            this.settings = settings;
        }

        public static class SettingsBean {
            @SerializedName("navi_settings")
            private JsonElement naviSettings;

            public JsonElement getNaviSettings() {
                return naviSettings;
            }

            public void setNaviSettings(JsonElement naviSettings) {
                this.naviSettings = naviSettings;
            }
        }
    }
}
