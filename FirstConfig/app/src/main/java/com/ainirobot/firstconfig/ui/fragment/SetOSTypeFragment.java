package com.ainirobot.firstconfig.ui.fragment;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.OSTypeBean;
import com.ainirobot.firstconfig.databinding.SetOSTypeBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.SetOSTypeViewModel;

import java.util.ArrayList;
import java.util.List;


/**
 * 配置系统类型（RobotOS系统还是AgentOS系统）
 */
public class SetOSTypeFragment extends BaseFragment<SetOSTypeViewModel, SetOSTypeBinding> {

    private static final String TAG = SetOSTypeFragment.class.getSimpleName();
    private MyAdapter mAdapter;


    @Override
    protected int getLayoutID() {
        return R.layout.fragment_os_type;
    }

    @Override
    protected void initData() {

        mBinding.setClickListener(this);
        mAdapter = new MyAdapter(getActivity());
        mBinding.lvTypes.setAdapter(mAdapter);
        mBinding.lvTypes.setOnItemClickListener((parent, view, position, id) -> {
            OSTypeBean OSTypeBean = (OSTypeBean) mAdapter.getItem(position);
            String osType = OSTypeBean.getOSType();
            Log.e(TAG, "set osType:" + osType);
            mViewModel.updateOSType(osType);
            itemClick(position);
        });

        mViewModel.getState().observe(this, OSTypeBeans -> {
            mAdapter.updateData(OSTypeBeans);
            mAdapter.notifyDataSetChanged();
        });
    }


    private void itemClick(int clickPosition) {
        int count = mAdapter.getCount();
        for (int i = 0; i < count; i++) {
            ((OSTypeBean) mAdapter.getItem(i)).setDefault(i == clickPosition);
        }
        mAdapter.notifyDataSetChanged();
    }

    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<OSTypeBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<OSTypeBean> getData() {
            return mData;
        }

        public void updateData(List<OSTypeBean> list) {
            mData.clear();
            mData.addAll(list);
            for (OSTypeBean bean: mData) {
                Log.e(TAG, "=============bean:" + bean.getOSName() + ", " + bean.getOSType());
            }
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvServerName = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            OSTypeBean bean = (OSTypeBean) mData.get(position);
            holder.tvServerName.setText(bean.getOSName());
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.select : R.drawable.unselect);
            return convertView;
        }

        class ViewHolder {
            TextView tvServerName;
            ImageView imSelect;
        }
    }
}
