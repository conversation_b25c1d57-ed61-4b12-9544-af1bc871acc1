/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.content.Context;
import android.util.Log;

import com.ainirobot.coreservice.client.Invoker;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.bean.StateData;

import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import androidx.annotation.NonNull;

public class BaseState extends AbstractState implements Invoker {
    private static final String TAG = BaseState.class.getSimpleName();

    private String mStateName;
    private boolean bIsAlive = false;
    private IStateListener mListener = null;
    protected Context mContext;
    private static final ReadWriteLock sLock = new ReentrantReadWriteLock();

    BaseState(int resId) {
        mContext = FirstConfigApplication.getAppContext();
        if (mContext != null) {
            mStateName = mContext.getString(resId);
        } else {
            Log.e(TAG, "Context is null");
        }
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        sLock.writeLock().lock();
        try {
            mListener = listener;
        } finally {
            sLock.writeLock().unlock();
            bIsAlive = true;
        }
    }

    @Override
    protected void doAction() {
    }

    @Override
    protected void exit() {
        sLock.writeLock().lock();
        try {
            mListener = null;
        } finally {
            sLock.writeLock().unlock();
            bIsAlive = false;
        }
    }

    @Override
    protected BaseState getNextState() {
        return null;
    }

    @Override
    protected BaseState getPreState() {
        return null;
    }

    @Override
    protected Object getData() {
        return null;
    }

    @Override
    protected boolean isAvailable() {
        return true;
    }

    public final String getStateName() {
        return mStateName;
    }

    @Override
    public final boolean isAlive() {
        return bIsAlive;
    }

    protected final void onSuccess() {
        if (!isAlive()) {
            Log.e(TAG, "onSuccess but cur state not alive");
            return;
        }
        IStateListener listener = null;
        sLock.readLock().lock();
        try {
            listener = mListener;
        } finally {
            sLock.readLock().unlock();
            if (listener != null) {
                listener.onSuccess();
            } else {
                Log.e(TAG, "onSuccess but no listener");
            }
        }
    }

    protected final void onError(@NonNull StateData data) {
        if (!isAlive()) {
            Log.e(TAG, "onError but cur state not alive:"
                    + data.code + ", msg:" + data.msg);
            return;
        }
        IStateListener listener = null;
        sLock.readLock().lock();
        try {
            listener = mListener;
        } finally {
            sLock.readLock().unlock();
            if (listener != null) {
                Log.e(TAG, "onError errno:" + data.code + ", msg:" + data.msg + ", id:" + data.id);
                listener.onError(data);
            } else {
                Log.e(TAG, "onError but no listener");
            }
        }
    }

    protected String generateErrorId() {
        return RobotSettings.getSystemSn() + "-" + System.currentTimeMillis();
    }
}
