package com.ainirobot.firstconfig.ui.fragment;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.SettingsScenarioList;
import com.ainirobot.firstconfig.databinding.SettingsScenarioBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.SettingsScenarioViewModel;
import com.ainirobot.firstconfig.utils.ResType;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.ainirobot.firstconfig.utils.ToastUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 分场景设置导航参数
 */
public class SettingsScenarioFragment extends BaseFragment<SettingsScenarioViewModel, SettingsScenarioBinding> {

    private String mLanguageCode;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_settings_scenario;
    }

    @Override
    protected void initData() {
        mLanguageCode = SharedPrefUtil.getInstance().getFirstConfigLanguageCode();
        showLoadingView();
        mBinding.setClickListener(this);
        MyAdapter adapter = new MyAdapter(getActivity());
        mBinding.contentLayout.setAdapter(adapter);
        mBinding.contentLayout.setOnItemClickListener((parent, view, position, id) -> {
            SettingsScenarioList.SettingsScenarioListBean paramsBean = (SettingsScenarioList.SettingsScenarioListBean) adapter.getItem(position);
            String scenarioId = paramsBean.getSettingsScenarioId();
            mViewModel.updatePlatform(scenarioId);
            SettingsScenarioFragment.this.itemClick(adapter, position);
        });

        mViewModel.getDataSuccessState().observe(this, paramsBeans -> {
            hideLoadingView();
            SettingsScenarioList.SettingsScenarioListBean bean = paramsBeans.get(0);
            bean.setDefault(true);
            mViewModel.updatePlatform(bean.getSettingsScenarioId());
            mBinding.tvSubtitle.setVisibility(View.VISIBLE);
            mBinding.confirmLayout.setVisibility(View.VISIBLE);
            mBinding.navigationConfirm.setText(R.string.finish_active);
            adapter.updateData(paramsBeans);
            adapter.notifyDataSetChanged();
        });
        mViewModel.getDataFailedState().observe(this, isEmpty -> {
            hideLoadingView();
            mBinding.failedLayout.setVisibility(View.VISIBLE);
            mBinding.confirmLayout.setVisibility(View.VISIBLE);
            mBinding.settingsScenarioImg.setBackgroundResource(ResType.SETTINGS_SCENARIO_ICON.getResIdByType());
            if (isEmpty) {//空界面
                mBinding.failedTitle.setText(R.string.current_scene_empty);
                mBinding.navigationConfirm.setText(R.string.skip);
            } else {//失败重试界面
                mBinding.failedTitle.setText(R.string.data_exception_retry);
                mBinding.navigationConfirm.setText(R.string.wx_qr_code_load_retry);
            }
        });
        mViewModel.getDataUpdateFailedState().observe(this, result -> {
            if (!result) {
                hideLoadingView();
                ToastUtil.showToast(getContext(), getString(R.string.activation_exception), Toast.LENGTH_SHORT);
            }
        });
        mViewModel.getConfirmClickState().observe(this, isDataSuccess -> {
            if (isDataSuccess) {
                SettingsScenarioFragment.this.loadingViewMantle();
            }
            SettingsScenarioFragment.this.showLoadingView();
        });
    }

    private void loadingViewMantle() {
        mBinding.loadingTitle.setText(R.string.activating_reboot);
        mBinding.loadingLayout.setBackgroundColor(0x55FFFFFF);
    }

    private void showLoadingView() {
        mBinding.failedLayout.setVisibility(View.GONE);
        mBinding.loadingLayout.setVisibility(View.VISIBLE);
        mBinding.loadingProgress.startAnimation(AnimationUtils.loadAnimation(getContext(), R.anim.loading_anim));
    }

    private void hideLoadingView() {
        mBinding.loadingProgress.clearAnimation();
        mBinding.loadingLayout.setVisibility(View.GONE);
    }

    private void itemClick(BaseAdapter adapter, int clickPosition) {
        int count = adapter.getCount();
        for (int i = 0; i < count; i++) {
            ((SettingsScenarioList.SettingsScenarioListBean) adapter.getItem(i)).setDefault(i == clickPosition);
        }
        adapter.notifyDataSetChanged();
    }

    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<SettingsScenarioList.SettingsScenarioListBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<SettingsScenarioList.SettingsScenarioListBean> getData() {
            return mData;
        }

        public void updateData(List<SettingsScenarioList.SettingsScenarioListBean> list) {
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            MyAdapter.ViewHolder holder = null;
            if (convertView == null) {
                holder = new MyAdapter.ViewHolder();
                convertView = mInflater.inflate(R.layout.item_settings_scenario, null);
                holder.tvScenario = convertView.findViewById(R.id.tv_scenario);
                holder.imSelect = convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (MyAdapter.ViewHolder) convertView.getTag();
            }
            SettingsScenarioList.SettingsScenarioListBean bean = mData.get(position);
            holder.tvScenario.setText(getScenarioName(bean));
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.select : R.drawable.unselect);
            return convertView;
        }

        class ViewHolder {
            TextView tvScenario;
            ImageView imSelect;
        }
    }

    private String getScenarioName(SettingsScenarioList.SettingsScenarioListBean bean) {
        SettingsScenarioList.SettingsScenarioListBean.SettingsScenarioBean settingsScenario = bean.getSettingsScenario();
        String defaultName = settingsScenario.getScenarioName();
        JsonElement scenarioNameIntl = settingsScenario.getScenarioNameIntl();
        if (null == scenarioNameIntl) {
            return defaultName;
        }
        JsonObject jsonObject = scenarioNameIntl.getAsJsonObject();
        String currentName = null;
        if (jsonObject.has(mLanguageCode)) {
            currentName = jsonObject.getAsJsonPrimitive(mLanguageCode).getAsString();
        }
        return TextUtils.isEmpty(currentName) ? defaultName : currentName;
    }
}
