/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import com.ainirobot.firstconfig.R;

/**
 *  恢厂后的首次激活流程：　
 *  首次开机初始化 --> 语言,时区,地区设置 --> 监听联网--> 检查绑定　--> 绑定激活成功 -->　同步服务端主语言　--> 自检 -->
 *  1.若自检成功则显示激活成功页 --> 点击建图
 *  2.若自检失败则显示自检失败。　
 */
public enum StateEnum {
    INIT(0,new InitState(R.string.init_state)),
    LANGUAGE(5, new LanguageState(R.string.language_time_setting)),
    CONFIGURE(10, new NetConfigState(R.string.configure_state)),
    CHECKING(20, new CheckingState(R.string.checking_state)),
    BINDING(30, new BindingState(R.string.binding_state)),
    REPORT_OS_TYPE(35, new ReportOSTypeState(R.string.report_os_type_state)),
    SERVERLANG(40, new ServerLanguageState(R.string.working_state)),
    INSPECT(45, new InspectState(R.string.device_inspect)),
    ERROR(50, new ErrorState(R.string.error_state)),
    SETTINGS_SCENARIO_LIST(60, new SettingsScenarioListState(R.string.settings_scenario_list)),
    SETTINGS_SCENARIO_UPLOAD(65, new SettingsScenarioUploadState(R.string.upload_settings_scenario)),
    SETTINGS_SCENARIO_INFO(70, new SettingsScenarioInfoState(R.string.settings_scenario_info));

    private int id;
    private BaseState state;

    StateEnum(int id, BaseState state) {
        this.id = id;
        this.state = state;
    }

    public int getId() {
        return id;
    }

    public BaseState getState() {
        return state;
    }
}
