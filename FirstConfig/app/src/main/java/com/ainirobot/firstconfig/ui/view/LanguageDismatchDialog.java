/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.firstconfig.ui.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.CloudServerBean;
import com.ainirobot.firstconfig.utils.ResUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.appcompat.app.AlertDialog;
import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * 服务端的主语言和客户端的不一致时，提示框.
 */
public class LanguageDismatchDialog extends AlertDialog implements View.OnClickListener{
    private static final String TAG = LanguageDismatchDialog.class.getSimpleName();

    private Context mContext;
    private TextView mConfirm, mCancel;
    private ConstraintLayout mLayout;
    private TextView mContent;
    private String mServerCode;
    private List<CloudServerBean> mList = new ArrayList<>();
    private boolean serverSwitched = false;
    private View mTitle;


    public LanguageDismatchDialog(Context context) {
        this(context,0);
        mContext = context;
    }

    public LanguageDismatchDialog(Context context, int theme) {
        super(context, theme);
        mContext = context;
    }

    private ClickListener mClickListener ;
    public interface ClickListener{
        void onConfirmClick();
        void onCancelClick();
    }

    public LanguageDismatchDialog setDialogClickListener(ClickListener listener){
        this.mClickListener = listener;
        return this;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_dialog);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 950;
        p.height = 650;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);
        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_parent);
        mTitle = findViewById(R.id.title);
        mContent = (TextView)findViewById(R.id.content);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mCancel = (TextView) findViewById(R.id.cancel);
        findViewById(R.id.lv_types).setVisibility(View.GONE);
        mConfirm.setOnClickListener(this);
        mCancel.setOnClickListener(this);
//        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
//            @Override
//            public void getOutline(View view, Outline outline) {
//                view.setClipToOutline(true);
//                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),30);
//            }
//        };
//        mLayout.setOutlineProvider(viewOutlineProvider);
    }


    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout,"alpha",0,1)
                .setDuration(170)
                .start();
        super.show();

        mTitle.setVisibility(View.INVISIBLE);
        mConfirm.setText(ResUtil.getString(R.string.cloud_reboot));
        Log.d(TAG, "show Cloud Dialog");
    }

    public void setContent(String content){
        mContent.setText(content);
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId){
            case R.id.confirm:
                if (mClickListener != null) {
                    mClickListener.onConfirmClick();
                }
                dismiss();
                break;
            case R.id.cancel:
                dismiss();
                break;
            default:
                Log.d(TAG, "default case");
                break;

        }
    }
}
