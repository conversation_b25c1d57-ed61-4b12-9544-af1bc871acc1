/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.ainirobot.firstconfig.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.ainirobot.firstconfig.R;


public class ToastUtil {

    private static ToastUtil mInstance = null;
    private Toast mToast;
    private TextView mTextView;

    public void init(Context context) {
        mToast = new Toast(context);
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View toastRoot = inflater.inflate(R.layout.layout_toast, null);
        mTextView = (TextView)toastRoot.findViewById(R.id.toast_text);
        mToast.setView(toastRoot);
        mToast.setGravity(Gravity.CENTER, 0, 395);
        setDuration(Toast.LENGTH_SHORT);
        setText(ResUtil.getString(R.string.network_reset_success));
    }

    public static ToastUtil getInstance() {
        if (mInstance == null) {
            mInstance = new ToastUtil();
        }
        return mInstance;
    }

    public ToastUtil setDuration(int duration) {
        if (mToast != null) {
            mToast.setDuration(duration);
        }
        return this;
    }

    public ToastUtil setText(CharSequence text) {
        if (mTextView != null) {
            mTextView.setText(text);
        }
        return this;
    }

    public void show() {
        if (mToast != null) {
            mToast.show();
        }
    }

    public static void showToast(final Context context, final String text, int duration) {

        if (isMainThread()) {
            Toast.makeText(context, text, duration).show();
        } else {
            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(new Runnable() {
                @Override
                public void run() {
                    Toast.makeText(context, text, duration).show();
                }
            });
        }
    }

    private static boolean isMainThread() {
        return Looper.getMainLooper() == Looper.myLooper();
    }

}
