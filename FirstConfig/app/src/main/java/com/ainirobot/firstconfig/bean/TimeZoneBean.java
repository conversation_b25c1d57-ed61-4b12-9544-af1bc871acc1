package com.ainirobot.firstconfig.bean;

public class TimeZoneBean {

    private String timeZoneCode;
    private String timeZoneName;
    private boolean isDefault;

    public TimeZoneBean(String timeZoneCode, String timeZoneName, boolean isDefault) {
        this.timeZoneCode = timeZoneCode;
        this.timeZoneName = timeZoneName;
        this.isDefault = isDefault;
    }

    public String getTimeZoneCode() {
        return timeZoneCode;
    }

    public void setTimeZoneCode(String timeZoneCode) {
        this.timeZoneCode = timeZoneCode;
    }

    public String getTimeZoneName() {
        return timeZoneName;
    }

    public void setTimeZoneName(String timeZoneName) {
        this.timeZoneName = timeZoneName;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    @Override
    public String toString() {
        return "TimeZone{" +
                "timeZoneCode='" + timeZoneCode + '\'' +
                ", timeZoneName='" + timeZoneName + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }
}
