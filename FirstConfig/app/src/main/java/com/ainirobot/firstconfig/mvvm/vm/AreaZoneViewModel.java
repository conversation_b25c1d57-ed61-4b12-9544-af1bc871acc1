package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.AreaBean;
import com.ainirobot.firstconfig.bean.AreaComparator;
import com.ainirobot.firstconfig.bean.ZoneEnum;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * 地区处理
 */
public class AreaZoneViewModel extends BaseViewModel<BaseModel> {

    private String mAreaZoneCode = "";
    private MutableLiveData<List<AreaBean>> mAreaZoneState = new MutableLiveData<>();
    private List<AreaBean> mList = new ArrayList<>();

    public AreaZoneViewModel(@NonNull Application application) {
        super(application);
    }


    public MutableLiveData<List<AreaBean>> getAreaZoneState(){
        return mAreaZoneState;
    }


    @Override
    public void onStart() {
        super.onStart();
        
        initAreaZoneData();
    }

    private void initAreaZoneData() {
        try {

            initAreaCode();
            Log.d(TAG, "default AreaZoneCode: "+mAreaZoneCode);

            Resources res = getApplication().getResources();
            XmlResourceParser xrp = res.getXml(R.xml.area_zone);
            while (xrp.getEventType() != XmlResourceParser.END_DOCUMENT) {
                if (xrp.getEventType() == XmlResourceParser.START_TAG) {
                    String name = xrp.getName();
                    if (name.equals("areazone")) {
                        //0，标识id，1标识名称
//                        map.put(xrp.getAttributeValue(1), xrp.getAttributeValue(0));
//                        list.add(xrp.getAttributeValue(1));

                        AreaBean zoneBean = new AreaBean(xrp.getAttributeValue(0), xrp.getAttributeValue(1),
                                xrp.getAttributeValue(0).equals(mAreaZoneCode));
                        mList.add(zoneBean);
                    }
                }
                xrp.next();
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            Collections.sort(mList,new AreaComparator());
            Log.d(TAG, "initAreaZoneData: "+mList);
            mAreaZoneState.postValue(mList);
        }
    }

    private void initAreaCode() {
        String areaCode = SharedPrefUtil.getInstance().getAreaZoneCode();
        if (TextUtils.isEmpty(areaCode)){
            String languageCode = SharedPrefUtil.getInstance().getFirstConfigLanguageCode();
            Log.d(TAG, "initAreaCode languageCode : "+languageCode);
            mAreaZoneCode = getDefaultAreazoneId(languageCode);
        }else {
            mAreaZoneCode = areaCode;
        }
    }

    /**
     * 产品逻辑：不同语言默认的地区对应的国家代码
     * @param code
     * @return
     */
    private String getDefaultAreazoneId(String code) {
        return ZoneEnum.getZoneEnumByCodeName(code).areaZone;
    }

    public void updateAreaZoneCode(String newCode){
        if (TextUtils.isEmpty(newCode)){
            Log.e(TAG, "updateAreaZoneCode failed , newCode is empty !");
            return;
        }
        this.mAreaZoneCode = newCode;
        SharedPrefUtil.getInstance().setAreaZoneCode(mAreaZoneCode);
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.butt_back:
                EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_TIME_ZONE),
                        FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                break;
            case R.id.butt_next:
                Log.d(TAG, "FirstConfig report area zone "+ mAreaZoneCode);
//                RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_TIME_ZONE_CODE, mAreaZoneCode);
                EventBus.getDefault().post(true, FirstConfigDef.EVEVTBUS_TAG_INIT_FINISH);
//                new TimeZoneReport().addTimeZone(mAreaZoneCode).addWay().report();
                break;
            default:
                break;
        }
    }

}
