package com.ainirobot.firstconfig.control.state;

import static com.ainirobot.firstconfig.utils.JsonUtils.getDoubleValue;
import static com.ainirobot.firstconfig.utils.JsonUtils.getFloatValue;
import static com.ainirobot.firstconfig.utils.JsonUtils.getIntValue;
import static com.ainirobot.firstconfig.utils.JsonUtils.getLongValue;
import static com.ainirobot.firstconfig.utils.JsonUtils.getStringValue;

import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.RoverConfig;
import com.ainirobot.firstconfig.bean.RoverConfigAnnotation;
import com.ainirobot.firstconfig.bean.SettingsScenarioData;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import org.simple.eventbus.EventBus;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 后续增加新配置，修改{@link  SettingsScenarioInfoState#updateParams(java.util.Map.Entry, java.util.Map)}即可
 * 1. 如果是Setting保存的配置，新增case进行处理保存
 * 2. 如果是保存在RoverConfig配置中，新增case，保存在map中即可
 */
public class SettingsScenarioInfoState extends BaseState {
    public static final int RESPONSE_SUCCEED = 1;
    public static final int RESPONSE_FAILED = 2;

    private boolean mSkip;
    private final Gson mGson;

    SettingsScenarioInfoState(int resId) {
        super(resId);
        mGson = new Gson();
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
        if (data instanceof Boolean) {
            mSkip = (boolean) data;
        }
    }

    @Override
    protected void doAction() {
        if (mSkip) {
            onSuccess();
            return;
        }
        SystemApi.getInstance().requestSettingsScenarioInfo(Definition.MODULE_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(getStateName(), "requestSettingsScenarioInfo : " + extraData);
                if (TextUtils.equals(message, Definition.SUCCEED)) {
                    try {
                        SettingsScenarioData scenarioData = mGson.fromJson(extraData, SettingsScenarioData.class);
                        SettingsScenarioData.SettingsScenarioBean scenarioBean;
                        SettingsScenarioData.SettingsScenarioBean.SettingsBean settingsBean;
                        if (null != scenarioData
                                && null != (scenarioBean = scenarioData.getSettingsScenario())
                                && null != (settingsBean = scenarioBean.getSettings())) {
                            updateLocalParams(settingsBean);
                        } else {
                            requestFinish(RESPONSE_FAILED);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        Log.d(getStateName(), "remoteQrcode parse error : " + e.getLocalizedMessage());
                        requestFinish(RESPONSE_FAILED);
                    }
                } else {
                    requestFinish(RESPONSE_FAILED);
                }
            }
        });
    }

    private void requestFinish(int state) {
        EventBus.getDefault().post(state, FirstConfigDef.EVENTBUS_TAG_SETTINGS_SCENARIO_INFO);
    }

    @Override
    protected BaseState getNextState() {
        return StateEnum.INSPECT.getState();
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.CONFIGURE.getState();
    }

    private void updateLocalParams(SettingsScenarioData.SettingsScenarioBean.SettingsBean settingsBean) {
        JsonElement naviSettingsBean;
        if (null == settingsBean || null == (naviSettingsBean = settingsBean.getNaviSettings())) {
            onSuccess();
            return;
        }
        int hasUpdate = 0;
        JsonObject jsonObject = naviSettingsBean.getAsJsonObject();
        Set<Map.Entry<String, JsonElement>> keys = jsonObject.entrySet();
        Map<String, JsonElement> roverSets = new HashMap<>();
        for (Map.Entry<String, JsonElement> data : keys) {
            hasUpdate = hasUpdate | updateParams(data, roverSets);
        }
        if (roverSets.isEmpty()) {
            if ((hasUpdate & 1) != 0) {
                EventBus.getDefault().post(RESPONSE_SUCCEED, FirstConfigDef.EVENTBUS_TAG_SETTINGS_SCENARIO_INFO);
            } else {
                onSuccess();
            }
        } else {
            updateRoverConfig(roverSets);
        }
    }

    private void updateRoverConfig(Map<String, JsonElement> roverSets) {
        SystemApi.getInstance().getNavigationConfig(Definition.MODULE_REQ_ID, new CommandListener() {
            @RequiresApi(api = Build.VERSION_CODES.N)
            @Override
            public void onResult(int result, String message, String extraData) {
                try {
                    RoverConfig roverConfig = mGson.fromJson(message, RoverConfig.class);
                    Field[] fields = roverConfig.getClass().getDeclaredFields();
                    for (Field field : fields) {
                        RoverConfigAnnotation annotation = field.getAnnotation(RoverConfigAnnotation.class);
                        if (null == annotation || !roverSets.containsKey(annotation.value())) {
                            continue;
                        }
                        field.setAccessible(true);
                        setRoverField(roverConfig, field, annotation, roverSets);
                    }
                    SystemApi.getInstance().setNavigationConfig(Definition.MODULE_REQ_ID, mGson.toJson(roverConfig), new CommandListener() {
                        @Override
                        public void onResult(int result, String message, String extraData) {
                            if (result == 1 && Definition.SUCCEED.equals(message)) {
                                requestFinish(RESPONSE_SUCCEED);
                            } else {
                                requestFinish(RESPONSE_FAILED);
                            }
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    requestFinish(RESPONSE_FAILED);
                }
            }
        });
    }

    private void setRoverField(RoverConfig roverConfig, Field field, RoverConfigAnnotation annotation, Map<String, JsonElement> roverSets) throws IllegalAccessException {
        try {
            Object target = field.get(roverConfig);
            String value = annotation.value();
            if (target instanceof Boolean) {
                int intValue = getIntValue(roverSets.get(value), value);
                if (intValue != -1) {
                    field.set(roverConfig, intValue == 1);
                }
            } else if (target instanceof Integer) {
                int intValue = getIntValue(roverSets.get(value), value);
                if (intValue != -1) {
                    field.set(roverConfig, intValue);
                }
            } else if (target instanceof String) {
                String stringValue = getStringValue(roverSets.get(value), value);
                if (!TextUtils.isEmpty(stringValue)) {
                    field.set(roverConfig, stringValue);
                }
            } else if (target instanceof Long) {
                long longValue = getLongValue(roverSets.get(value), value);
                if (longValue != -1) {
                    field.set(roverConfig, longValue);
                }
            } else if (target instanceof Double) {
                double doubleValue = getDoubleValue(roverSets.get(value), value);
                if (doubleValue != -1) {
                    field.set(roverConfig, doubleValue);
                }
            } else if (target instanceof Float) {
                double floatValue = getFloatValue(roverSets.get(value), value);
                if (floatValue != -1) {
                    field.set(roverConfig, floatValue);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int updateParams(Map.Entry<String, JsonElement> data, Map<String, JsonElement> roverSets) {
        String paramsKey = data.getKey();
        JsonElement paramsValue = data.getValue();
        switch (paramsKey) {
            case "robot_setting_navigation_break_mode_level"://刹车档位
                String brakeGear = getStringValue(paramsValue, paramsKey);
                if (!TextUtils.isEmpty(brakeGear)) {
                    RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_NAVIGATION_BREAK_MODE_LEVEL, brakeGear);
                    return 1;
                }
            case "robot_setting_navigation_start_mode_level"://启动档位
                String startGear = getStringValue(paramsValue, paramsKey);
                if (!TextUtils.isEmpty(startGear)) {
                    RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_NAVIGATION_START_MODE_LEVEL, startGear);
                    return 1;
                }
            case "enable_graph_leading_planner_obspoints":
                roverSets.put(data.getKey(), data.getValue());
                break;
            case "legs_inflation_level":
                roverSets.put(data.getKey(), data.getValue());
                break;
        }
        return 0;
    }
}
