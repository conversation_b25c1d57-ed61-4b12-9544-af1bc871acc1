package com.ainirobot.firstconfig.bean;

public class OSTypeBean {
    private String osType;
    private String osName;
    private boolean isDefault; // true表示 默认是选中的

    public OSTypeBean(String osType, String osName, boolean isDefault) {
        this.osType = osType;
        this.osName = osName;
        this.isDefault = isDefault;
    }

    public String getOSName() {
        return osName;
    }

    public void setOSName(String serverName) {
        this.osName = serverName;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public String getOSType() {
        return osType;
    }

    public void setOSType(String osType) {
        this.osType = osType;
    }

    @Override
    public String toString() {
        return "OSTypeBean{" +
                "osType='" + osType + '\'' +
                ", osName='" + osName + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }
}
