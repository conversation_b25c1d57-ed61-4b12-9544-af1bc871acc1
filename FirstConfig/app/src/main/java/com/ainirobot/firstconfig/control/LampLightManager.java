package com.ainirobot.firstconfig.control;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;

public class LampLightManager {
    private static final String TAG = LampLightManager.class.getSimpleName();

    private int mCurrentColor = 0;
    private static final String BLUE = "5493FF";
    private static final String RED = "5493FF";
    public static final String YELLOW = "5493FF";

    private static volatile LampLightManager mInstance;

    private LampLightManager() {}

    public static LampLightManager getInstance() {
        if (mInstance == null) {
            synchronized (LampLightManager.class) {
                if (mInstance == null) {
                    mInstance = new LampLightManager();
                }
            }
        }
        return mInstance;
    }

    private LightStatusListener mStatusListener = new LightStatusListener();
    class LightStatusListener extends StatusListener{
        @Override
        public void onStatusUpdate(String type, String data) {
            Log.d(TAG, "onStatusUpdate ... type: "+data);
            switch (data) {
                case "0":
                    if (SystemApiProxy.getInstance().isUseAutoEffectLed()) {
                        SystemApiProxy.getInstance().setBottomLedEffect(0,
                                Definition.LED_EFFECT_BLUENORMAL, null);
                        if (ProductInfo.isSlimProduct()) {
                            SystemApiProxy.getInstance().setTrayLedEffect(0,
                                    Definition.LED_EFFECT_BLUENORMAL, null);
                        }
                    }else {
                        setLightColor(Integer.parseInt(BLUE, 16));
                    }
                    break;
                case "1":
                    if (SystemApiProxy.getInstance().isUseAutoEffectLed()) {
                        SystemApiProxy.getInstance().setBottomLedEffect(0,
                                Definition.LED_EFFECT_REDNORMAL, null);
                        if (ProductInfo.isSlimProduct()) {
                            SystemApiProxy.getInstance().setTrayLedEffect(0,
                                    Definition.LED_EFFECT_REDNORMAL, null);
                        }
                    }else {
                        setLightColor(Integer.parseInt(RED, 16));
                    }
                    break;
                default:
                    Log.d(TAG, "onStatusUpdate case default");
                    break;
            }
        }
    }

    public void startLampLightListener() {
        // monitor emergency
        SystemApiProxy.getInstance().registerStatusListener(Definition.STATUS_EMERGENCY, mStatusListener);
    }

    public void unRegisterLightListener(){  //解注册急停事件
        SystemApiProxy.getInstance().unregisterStatusListener(mStatusListener);
        mStatusListener = null;
    }


    private void showLightColor() {
        if (mCurrentColor == 0) {
            return;
        }
        Log.d(TAG, "set mCurrentColor ... ");
        SystemApiProxy.getInstance().setLambColor(0, 0, mCurrentColor);
        SystemApiProxy.getInstance().setLambColor(0, 1, mCurrentColor);
        SystemApiProxy.getInstance().setLambColor(0, 2, mCurrentColor);
    }

    public void setLightColor(int color) {
        if (color != 0) {
            mCurrentColor = color;
        }
        showLightColor();
    }

}
