package com.ainirobot.firstconfig.proxy;

import android.util.Log;

import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.control.SpeechManager;

import org.simple.eventbus.EventBus;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SystemApiUtil {
    private static final String TAG = "SystemApiUtil";
    private volatile static boolean isPending = true;
    //尝试的次数
    private static int mAttemptNum = 0;

    public synchronized static boolean connectSystemApi() {
        if (isSystemConnected()) {
            EventBus.getDefault().post(true);
            return true;
        }
        connect();
        return isSystemConnected();
    }

    public static boolean isSystemConnected() {
        return SystemApi.getInstance().isApiConnectedService();
    }

    private static void connect() {
        if (isPending) {
            isPending = false;
            Log.d(TAG, "initSystemApi start ");
            startConnectInner();
            startThread();
        }
    }

    public static void disconnect() {
        SystemApi.getInstance().disconnect(FirstConfigApplication.getAppContext());
    }

    private static void startThread() {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                while (!isSystemConnected()) {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (!isSystemConnected()) {
                        mAttemptNum ++;
                        Log.d(TAG, "自动重连次数=" + mAttemptNum);
                        startConnectInner();
                    }
                    Log.d(TAG, "isSystemConnected=" + isSystemConnected());
                }
            }
        });
    }

    private static void startConnectInner() {
        Log.d(TAG, "startConnectInner");
        SystemApi.getInstance().connect(FirstConfigApplication.getAppContext(), new ApiListenerAdapter() {
            @Override
            public void handleApiConnected() {
                Log.d(TAG, "handleApi Connected: ");
                isPending = true;
                mAttemptNum = 0;
                EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_SYSTEM_API_CONNECT);
                SpeechManager.getInstance().connectSpeechService();
            }

            @Override
            public void handleApiDisconnected() {
                Log.d(TAG, "handleApi Disconnected: ");
                EventBus.getDefault().post(false, FirstConfigDef.EVENTBUS_TAG_SYSTEM_API_CONNECT);
                isPending = true;
            }
        });
    }

}
