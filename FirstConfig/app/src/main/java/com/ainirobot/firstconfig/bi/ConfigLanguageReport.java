package com.ainirobot.firstconfig.bi;

import com.ainirobot.coreservice.client.upload.bi.BaseBiReport;

public class ConfigLanguageReport extends BaseBiReport {

    private static final String TABLE_NAME = "gb_os_language";
    private static final String CTIME = "ctime";
    private static final String LANGUAGE = "os_language";
    private static final String WAY = "way";

    public ConfigLanguageReport() {
        super(TABLE_NAME);
    }

    public ConfigLanguageReport addLanguage(String languageCode) {
        addData(LANGUAGE, languageCode);
        return this;
    }

    public ConfigLanguageReport addWay() {
        addData(WAY, 1);
        return this;
    }

    @Override
    public void report() {
        addData(CTIME, System.currentTimeMillis());
        super.report();
    }
}
