/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *     *
 *     * Licensed under the Apache License, Version 2.0 (the "License");
 *     * you may not use this file except in compliance with the License.
 *     * You may obtain a copy of the License at
 *     *
 *     *      http://www.apache.org/licenses/LICENSE-2.0
 *     *
 *     * Unless required by applicable law or agreed to in writing, software
 *     * distributed under the License is distributed on an "AS IS" BASIS,
 *     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     * See the License for the specific language governing permissions and
 *     * limitations under the License.
 */

package com.ainirobot.firstconfig;

import android.app.Application;
import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.firstconfig.utils.DimensionUtils;
import com.ainirobot.firstconfig.utils.SystemUtils;

public class FirstConfigApplication extends Application{
    private static final String TAG = FirstConfigApplication.class.getSimpleName();
    private static Context mContext = null;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "FirstConfig app onCreate");
        mContext = getApplicationContext();
        DimensionUtils.initDimension(getApplicationContext());
//        SystemUtils.enableStatusBar(); // 招财framework 层默认配置是false,表示不支持三指下拉, 后续其他平台也要默认false.
        SystemUtils.loadIME();
    }

    public static Context getAppContext(){
        return mContext;
    }




}
