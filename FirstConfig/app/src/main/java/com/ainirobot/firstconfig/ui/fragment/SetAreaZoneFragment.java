package com.ainirobot.firstconfig.ui.fragment;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.SectionIndexer;
import android.widget.TextView;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.AreaBean;
import com.ainirobot.firstconfig.databinding.AreaZoneBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.AreaZoneViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置地区
 */
public class SetAreaZoneFragment extends BaseFragment<AreaZoneViewModel, AreaZoneBinding> {

    private static final String TAG = SetAreaZoneFragment.class.getSimpleName();

    private MyAdapter mAdapter;


    @Override
    protected int getLayoutID() {
        return R.layout.fragment_area_zone;
    }

    @Override
    protected void initData() {
        mViewModel.getAreaZoneState().observe(this, areaBeanList -> {
            mAdapter.updateData(areaBeanList);
            mAdapter.notifyDataSetChanged();
            smoothToDefault();
        });

        mBinding.setClickListener(this);
        mAdapter = new MyAdapter(getActivity());
        mBinding.lvAreaZone.setAdapter(mAdapter);
        mBinding.lvAreaZone.setOnItemClickListener((parent, view, position, id) -> {
            Log.d(TAG, "onItem click " + position);
            mViewModel.updateAreaZoneCode(mAdapter.getItem(position).getCountryCode());
            clickPosition(position);
        });

        //设置右侧索引条监听
        mBinding.sideBar.setOnTouchingLetterChangedListener(s -> {
            //该字母首次出现的位置
            Log.d(TAG, "OnTouchingLetterChanged s :" + s);
            int position = mAdapter.getPositionForSection(s.charAt(0));// char 字符默认转换为int 的ASCII码
            Log.d(TAG, "OnTouchingLetterChanged posi : " + position);
            if (position != -1) {
                mBinding.lvAreaZone.setSelection(position + 1);
            }
        });

    }


    private void smoothToDefault() {
        List<AreaBean> dataList = mAdapter.getData();
        for (AreaBean bean : dataList) {
            if (bean.isDefault()) {
                int i = dataList.indexOf(bean);
                Log.d(TAG, "smoothToDefault dest :" + i);
                mBinding.lvAreaZone.setSelection(i);
            }
        }
    }

    private void clickPosition(int clickPosition) {
        int count = mAdapter.getCount();
        for (int i = 0; i < count; i++) {
            mAdapter.getItem(i).setDefault(i == clickPosition);
        }
        mAdapter.notifyDataSetChanged();
    }


    class MyAdapter extends BaseAdapter implements SectionIndexer {

        private LayoutInflater mInflater;
        private List<AreaBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<AreaBean> getData() {
            return mData;
        }

        public void updateData(List<AreaBean> list) {
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public AreaBean getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvLanguage = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            AreaBean bean = (AreaBean) mData.get(position);
            holder.tvLanguage.setText(bean.getCountryName());
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.select : R.drawable.unselect);

            return convertView;
        }


        @Override
        public Object[] getSections() {
            return null;
        }

        /**
         * 根据分类的首字母的Char ascii值,获取其第一次出现该首字母的位置
         *
         * @param section 首字母的Char ascii值
         * @return
         */
        @Override
        public int getPositionForSection(int section) {
            for (int i = 0; i < getCount(); i++) {
                String sortStr = mData.get(i).getCountryCode();
                char firstChar = sortStr.toUpperCase().charAt(0);
                if (firstChar == section) {
                    return i;
                }
            }
            return -1;
        }

        /**
         * 根据ListView的当前位置获取分类的首字母的char ascii值
         *
         * @param position ListView的当前位置
         * @return
         */
        @Override
        public int getSectionForPosition(int position) {
            return mData.get(position).getCountryCode().charAt(0);
        }

        class ViewHolder {
            TextView tvLanguage;
            ImageView imSelect;
        }
    }


}
