package com.ainirobot.firstconfig.utils;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;


import com.ainirobot.firstconfig.bean.MapBean;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

public class MapFileUtils {
    private final static String TAG = MapFileUtils.class.getSimpleName();
    private final static String CONFIG_DIR = "/robot/config";
    private static final String CONFIG_FILE = "navigation.properties";
    private final static String MAP_DIR = "/robot/map";

    private final static String ROBOT_MAP_DIR = Environment.getExternalStorageDirectory() + MAP_DIR;
    private final static String FRAME_DATA = "data.zip";
    private final static String MAP_PGM = "pgm.zip";
    private final static String MD5 = "MD5";

    public static String getMapDir(String name) {
        return ROBOT_MAP_DIR + File.separator + name;
    }

    public static String getMapPgmFilePath(String name) {
        String mapPath = ROBOT_MAP_DIR + File.separator + name + File.separator + MAP_PGM;
        Log.d(TAG, "getMapPgmFilePath. path:" + mapPath);
        return mapPath;
    }

    public static String getMapDataFilePath(String name) {
        String dataPath = ROBOT_MAP_DIR + File.separator + name + File.separator + FRAME_DATA;
        Log.d(TAG, "getMapDataFilePath. path:" + dataPath);
        return dataPath;
    }


    public static String getMapMd5(String mapName) {
        return getConfig(MD5 + "_" + mapName);
    }

    private static String getConfig(String key) {
        Properties properties = getProperties();
        if (properties != null) {
            return properties.getProperty(key);
        }
        return null;
    }

    private static Properties getProperties() {
        Properties properties = new Properties();
        File file = getConfigFile();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            properties.load(fis);
        } catch (IOException e) {
            e.printStackTrace();
            return properties;
        } finally {
            IOUtils.close(fis);
        }
        return properties;
    }

    private static File getConfigFile() {
        File root = Environment.getExternalStorageDirectory();
        File dir = new File(root, CONFIG_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, CONFIG_FILE);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    public static boolean isMapFileValid(String name) {
        boolean isValid = false;
        String mapPgm = getMapPgmFilePath(name);
        String mapData = getMapDataFilePath(name);
        String md5 = getMapMd5(name);
        if (mapPgm == null || mapPgm.isEmpty()
                || mapData == null || mapData.isEmpty() || TextUtils.isEmpty(md5)) {
            isValid = false;
        } else {
            File pgmF = new File(mapPgm);
            File dataF = new File(mapData);
            if (pgmF.exists() && dataF.exists()) {
                isValid = true;
            }
        }

        return isValid;
    }

    public static MapBean[] getMapList() {
        File root = Environment.getExternalStorageDirectory();
        File mapDir = new File(root, MAP_DIR);

        if (!mapDir.isDirectory() || mapDir.list().length == 0) {
            return null;
        }

        MapBean[] mapList = new MapBean[mapDir.list().length];
        File[] files = mapDir.listFiles();
        for (int i = 0; i < mapList.length; i++) {
            mapList[i] = new MapBean(files[i]);
        }

        return mapList;
    }

    public static MapBean getMatchMap(String mapName) {
        if (mapName == null || mapName.isEmpty()) {
            return null;
        }

        MapBean[] mapBeans = getMapList();
        if (mapBeans == null) {
            return null;
        }

        for (MapBean mapBean : mapBeans) {
            if (mapBean.getFileName() != null && mapBean.getFileName().equals(mapName)) {
                return mapBean;
            }
        }

        return null;
    }

    public static boolean queryHasLocalMap(){
        boolean isHaveLocalMap = false;
        MapBean[] mapList = MapFileUtils.getMapList();
        if (mapList == null) {
            return false;
        }
        for (int i = 0; i < mapList.length; i++) {
            MapBean bean = mapList[i];
            Log.d(TAG,"queryLocalMap mapList : "+bean.toString());
            boolean valid = MapFileUtils.isMapFileValid(bean.getFileName());
            if (valid){
                isHaveLocalMap = true;
                break;
            }
        }
        return isHaveLocalMap;
    }


}
