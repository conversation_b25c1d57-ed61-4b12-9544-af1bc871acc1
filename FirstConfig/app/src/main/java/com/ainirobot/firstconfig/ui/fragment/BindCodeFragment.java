/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.databinding.QrCodeBindingOverSea;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.QrCodeViewModel;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.ui.view.CloudDialog;
import com.ainirobot.firstconfig.utils.CloudUtils;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;
import com.ainirobot.firstconfig.utils.ToastUtil;

/**
 * 海外机器 仅仅使用绑定码 激活机器。
 */
public class BindCodeFragment extends BaseFragment<QrCodeViewModel, QrCodeBindingOverSea> {

    private static final String TAG = BindCodeFragment.class.getSimpleName();

    private boolean isInvalid = true;   // 是否绑定码无效
    private String mCodePC;
    private CloudDialog mCloudDialog = null;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_qr_code_oversea;
    }

    @Override
    protected void initData() {
        Log.d(TAG, "initData: ");
        mBinding.setClickListener(this);
        mViewModel.getRetryClickableState().observe(this, clickable -> {
            Log.d(TAG, " observe Retry clickable : " + clickable);
            mBinding.qrCodeLoadRetry.setClickable(clickable);
            mBinding.qrCodeLoadRetry.setPressed(!clickable);
        });

        mViewModel.getInvalidState().observe(this, invalid -> {
            // 监听二维码或绑定码是否失效
            isInvalid = invalid;
            if (invalid) {
                mCodePC = "";
                showExpiresInUI();
            }
        });

        mViewModel.getCloudServerState().observe(this, aBoolean -> {
            if (aBoolean) {
                //显示切换服务器弹框
                showCloudServerDialog();
                mBinding.bindRobotPrompt.setText(ResUtil.getString(R.string.bind_robot_prompt, getCurrentUrlShortener()));
            }
        });

        Bundle bundle = getArguments(); // getArguments , 在 fragment 初始化时默认是null
        if (bundle != null) {
            mCodePC = bundle.getString(Definition.BIND_CODE);
            isInvalid = false;
        }
        mBinding.currentCloudServer.setText(ResUtil.getString(R.string.cloud_current, getCurrentServer()));

        mBinding.bindRobotPrompt.setText(ResUtil.getString(R.string.bind_robot_prompt, getCurrentUrlShortener()));

    }

    /**
     * 云服务节点选择弹窗
     */
    private void showCloudServerDialog() {
        mCloudDialog = new CloudDialog(getActivity());
        mCloudDialog.setDialogClickListener(new CloudDialog.ClickListener() {
            @Override
            public void onConfirmClick() {
                mBinding.currentCloudServer.setText(ResUtil.getString(R.string.cloud_current, getCurrentServer()));
            }

            @Override
            public void onCancelClick() {

            }
        });
        mCloudDialog.show();
    }

    private String getCurrentServer() {
        String currentServer = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);;
        Log.d(TAG, "getCurrentServer: "+ currentServer);
        return CloudUtils.getServerResName(currentServer);
    }

    private String getCurrentUrlShortener() {
        String currentServer = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);;
        Log.d(TAG, "getCurrentServer: "+ currentServer);
        return CloudUtils.getServerUrl(currentServer);
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "onStart:" + isInvalid);

        showLoading();

        if (!isInvalid) {
            if (!TextUtils.isEmpty(mCodePC)) {
                showQrCodeSuccess();
            } else {
                showQrCodeFail();
            }
        } else {
            showExpiresInUI();
        }
    }


    private void showQrCodeSuccess() {
        Log.d(TAG, "showQrCodeSuccess ...");
        String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG, "systemLanguage = " + systemLanguage);
        mBinding.bindCodeTx.setText(mCodePC);
        mBinding.snNumberTx.setText(SystemUtils.getLast6SystemSerialNo());
        mBinding.qrCodeLoading.setText("");
        mBinding.qrCodeLoadRetry.setVisibility(View.GONE);
        mBinding.cloudServerContainer.setVisibility(View.VISIBLE);
    }

    private void showQrCodeFail() {
        Log.d(TAG, "showQrCodeFail ...");
        mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading_fail));
        mBinding.cloudServerContainer.setVisibility(View.GONE);
        mBinding.qrCodeLoadRetry.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoadRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
        ToastUtil.showToast(FirstConfigApplication.getAppContext(),
                ResUtil.getString(R.string.disconnect_dialog_content),
                Toast.LENGTH_SHORT);
    }

    private void showLoading() {
        Log.d(TAG, "showLoading ...");
        mBinding.bindCodeTx.setText("");
        mBinding.snNumberTx.setText("");
        mBinding.qrCodeLoading.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading));
        mBinding.qrCodeLoadRetry.setVisibility(View.GONE);
        mBinding.cloudServerContainer.setVisibility(View.VISIBLE);
    }

    /**
     * 二维码或者绑定码失效
     */
    private void showExpiresInUI() {
        Log.d(TAG, "showExpiresInUI ...");
        mBinding.bindCodeTx.setText("");
        mBinding.snNumberTx.setText("");
        mBinding.qrCodeLoading.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.binding_code_expired));
        mBinding.cloudServerContainer.setVisibility(View.GONE);
        mBinding.qrCodeLoadRetry.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoadRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mCloudDialog != null && mCloudDialog.isShowing()){
            mCloudDialog.dismiss();
        }
        Log.d(TAG, "onDestroy ");
    }

}
