package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.presenter.net.INetConnectionChangeListener;
import com.ainirobot.firstconfig.presenter.net.NetManager;
import com.ainirobot.firstconfig.utils.WifiUtils;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;
import org.simple.eventbus.ThreadMode;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

public class QrCodeViewModel extends BaseViewModel<BaseModel> implements INetConnectionChangeListener {


    private MutableLiveData<Boolean> retryClickableState = new MutableLiveData<>();
    private MutableLiveData<Boolean> invalidState = new MutableLiveData<>();
    private MutableLiveData<Boolean> cloudServerState = new MutableLiveData<>();


    public MutableLiveData<Boolean> getRetryClickableState() {
        return retryClickableState;
    }

    public MutableLiveData<Boolean> getInvalidState() {
        return invalidState;
    }

    public MutableLiveData<Boolean> getCloudServerState(){
        return cloudServerState;
    }

    public QrCodeViewModel(@NonNull Application application) {
        super(application);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
        NetManager.getInstance().registerNetReceiver();
    }


    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_QRCODE_OR_BINDCODE_INVALIDED)
    public void onQrCodeOrBindCodeInvalid(boolean isInvalid) {
        Log.d(TAG, "onQrCodeOrBindCodeInvalid isInvalid: " + isInvalid);
        invalidState.postValue(isInvalid);
    }

    @Override
    protected void viewClick(@IdRes int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.qr_code_load_retry:
                retryClickableState.postValue(false);
                requestQrCode();
                break;
            case R.id.title_leftback:
                Log.d(TAG, "title click back");
                EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_BACK_PRE_PAGE);
                break;
            case R.id.not_this:
                cloudServerState.postValue(true);
                break;
            default:
                Log.d(TAG, "default case");
                break;
        }
    }

    @Override
    public void onConnectionChanged(int state) {
        Log.d(TAG, "onConnectionChanged ==== ");
        if (WifiUtils.isConnected(FirstConfigApplication.getAppContext())) {
            Log.d(TAG, "onConnectionChanged  requestQrCode");
            requestQrCode();
        }
    }

    private void requestQrCode() {
        EventBus.getDefault().post(true,
                FirstConfigDef.EVENTBUS_TAG_REFRESH_QRCODE);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        retryClickableState.postValue(true);
        EventBus.getDefault().unregister(this);
        NetManager.getInstance().unRegisterNetReceiver();

    }
}
