/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.firstconfig.presenter.net;

import android.content.IntentFilter;

import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.receiver.NetworkConnectReceiver;


public class NetManager {

    private INetConnectionChangeListener listener = null;
    private NetworkConnectReceiver connectReceiver;

    private NetManager() {
    }

    private static class InstanceHolder {
        static final NetManager instance = new NetManager();
    }

    public static NetManager getInstance() {
        return InstanceHolder.instance;
    }

    public void registerNetReceiver(){
        IntentFilter filter = new IntentFilter();
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
        filter.addAction("android.net.conn.INET_CONDITION_ACTION");
        connectReceiver = new NetworkConnectReceiver();
        FirstConfigApplication.getAppContext().registerReceiver(connectReceiver,filter);
    }

    public void unRegisterNetReceiver(){
        if (connectReceiver != null) {
            FirstConfigApplication.getAppContext().unregisterReceiver(connectReceiver);
            connectReceiver = null;
        }
    }


    public void registerConnectionChangedLister(INetConnectionChangeListener changeListener) {
        this.listener = changeListener;
    }

    public void unRegisterConnectionChangedLister(){
        listener = null;
    }

    public void notifyConnectionChanged(int state) {
//        if (count == 0) {//注册网络监听后会立即收到此事件，因此对以下操作进行屏蔽，仅在之后的监听执行任务
//            count++;
//            return;
//        }
        if (listener != null) {
            listener.onConnectionChanged(state);
        }
    }

}
