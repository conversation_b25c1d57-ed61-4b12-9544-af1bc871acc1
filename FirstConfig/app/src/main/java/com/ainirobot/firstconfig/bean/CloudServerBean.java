package com.ainirobot.firstconfig.bean;

/**
 * 海外服务器域名切换实例
 * 海外的项目才会用到该类
 */
public class CloudServerBean {
    private String server_id;
    private String server_name;
    private boolean isDefault; // true表示 默认是选中的

    public CloudServerBean(String server_id, String server_name, boolean isDefault) {
        this.server_id = server_id;
        this.server_name = server_name;
        this.isDefault = isDefault;
    }

    public String getServer_name() {
        return server_name;
    }

    public void setServer_name(String server_name) {
        this.server_name = server_name;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public String getServer_id() {
        return server_id;
    }

    public void setServer_id(String server_id) {
        this.server_id = server_id;
    }

    @Override
    public String toString() {
        return "CloudServerBean{" +
                "server_id='" + server_id + '\'' +
                ", server_name='" + server_name + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }
}
