package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.CloudServerBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.utils.CloudUtils;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * 只有海外项目才会用到.
 * 配置云服务
 */
public class CloudServerViewModel extends BaseViewModel<BaseModel> {

    private List<CloudServerBean> mList = new ArrayList<>();
    private MutableLiveData<List<CloudServerBean>> state = new MutableLiveData<>();
    private MutableLiveData<Boolean> nextState = new MutableLiveData<>();
    private String mServerCode;
    private boolean mServerSwitched = false;

    public CloudServerViewModel(@NonNull Application application) {
        super(application);
    }

    public MutableLiveData<List<CloudServerBean>> getState() {
        return state;
    }

    public MutableLiveData<Boolean> getNextState(){
        return nextState;
    }

    @Override
    public void onStart() {
        super.onStart();

        initCloudServers();
    }

    private void initCloudServers() {

        String configCloud = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        if (TextUtils.isEmpty(configCloud)){
            configCloud = Definition.CloudServerZone.DEFAULT.getValue();
        }
        mServerCode = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "initCloudServers last localServerCode: " + mServerCode + ", systemServer :" + configCloud);
        mList.addAll(CloudUtils.getAllCloudServerBean(configCloud, mServerCode));
        state.postValue(mList);
    }


    public void updateCloudServer(String newCode) {
        if (TextUtils.isEmpty(newCode)) {
            Log.e(TAG, "updateAreaZoneCode failed , newCode is empty !");
            return;
        }
        mServerSwitched = isServerSwitched(newCode);
        nextState.postValue(mServerSwitched);
        mServerCode = newCode;
    }

    private boolean isServerSwitched(String newCode) {
        String localDefaultCode = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "isServerSwitched defaultCode : " + localDefaultCode);
        //根据服务器区域名称，判断是否切换了
        return CloudUtils.isServerSwitched(newCode,localDefaultCode);
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.butt_back:
                if (ProductInfo.isMeissa2() || ProductInfo.isMiniProduct()) {
                    EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_OS_TYPE),
                            FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                } else {
                    EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_LANGUAGE),
                            FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                }
                break;
            case R.id.butt_next:

                SystemApiProxy.getInstance().setRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE, mServerCode);
                SystemApiProxy.getInstance().setRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE, CloudUtils.getServerZone(mServerCode));
                if (mServerSwitched){
                    reboot();
                }else {
                    EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_TIME_ZONE),
                            FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                }
                break;
            default:
                break;
        }
    }

    private void reboot(){
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        getApplication().startActivity(intent);
    }

}
