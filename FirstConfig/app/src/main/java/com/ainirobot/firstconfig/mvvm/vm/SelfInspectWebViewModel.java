package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;
import org.simple.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * 招财海外版本，自检显示 WebView
 */
public class SelfInspectWebViewModel extends BaseViewModel<BaseModel> {

    private MutableLiveData<Boolean> mInspectFinish = new MutableLiveData<>(false);

    public SelfInspectWebViewModel(@NonNull Application application) {
        super(application);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.butt_next:
                Log.d(TAG, "viewClick:butt_next:");
                EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_BIND_SUCCESS),
                        FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                break;
            default:
                break;
        }
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_INSPECTION_SUCCESS)
    private void onEventBus_inspectSuccess(boolean status) {
        Log.d(TAG, "onEventBus_inspectSuccess: status=" + status);
        mInspectFinish.postValue(true);
    }

    public MutableLiveData<Boolean> getInspectFinish() {
        return mInspectFinish;
    }
}
