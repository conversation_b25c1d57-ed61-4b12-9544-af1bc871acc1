/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.app.Activity;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Message;

import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.control.SpeechManager;
import com.ainirobot.firstconfig.databinding.QrCodeBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.QrCodeViewModel;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;
import com.ainirobot.firstconfig.utils.WeakHandler;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class QRcodeFragment extends BaseFragment<QrCodeViewModel, QrCodeBinding>  {

    private static final String TAG = QRcodeFragment.class.getSimpleName();
    private static final int MSG_QRCODE_COMPLETE = 0x001;

    private MyWeakHandler mHandler;
    private boolean hasPlay = false;
    private boolean isInvalidQrcode = true;
    private String mUrl, mCodePC;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_qr_code;
    }

    @Override
    protected void initData() {
        Log.d(TAG, "initData: ");
        mHandler = new MyWeakHandler(getActivity());
        mBinding.setClickListener(this);

        mViewModel.getRetryClickableState().observe(this, clickable -> {
            Log.d(TAG, " observe Retry clickable : " + clickable);
            mBinding.qrCodeLoadRetry.setClickable(clickable);
            mBinding.qrCodeLoadRetry.setPressed(!clickable);
        });

        mViewModel.getInvalidState().observe(this, invalid ->{
            // 监听二维码或绑定码是否失效
            isInvalidQrcode = invalid;
            if (invalid){
                mCodePC = "";
                mUrl = "";
                showExpiresInUI();
            }
        });

        Bundle bundle = getArguments();
        if (bundle != null) {
            try {
                String data = bundle.getString(Definition.QRCODE_DATA);
                mCodePC = bundle.getString(Definition.BIND_CODE);
                String decodeStr = new String(Base64.decode(data, Base64.DEFAULT));
                JsonObject obj = new JsonParser().parse(decodeStr).getAsJsonObject();
                if (obj != null && obj.has("wx_url")) {
                    mUrl = obj.get("wx_url").getAsString();
                }
                if (!TextUtils.isEmpty(mUrl)) {
                    SharedPrefUtil.getInstance().setWxUrl(mUrl);
                }
            } catch (Exception e){
                e.printStackTrace();
            }
            isInvalidQrcode = false;
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG,  "QRcodeFragment onStart:" + isInvalidQrcode);

        showLoading();
        if (!isInvalidQrcode) {
            if (!TextUtils.isEmpty(mUrl)) {
                createQrCode(mUrl);
            } else {
                showQrCodeFail();
            }
        } else {
            showExpiresInUI();
        }
    }

    private void createQrCode(final String qrUrl) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Bitmap centerLogo = ResUtil.getCenterLogoBitmap(R.drawable.first_qrcode_logo);
                Bitmap qrImageBitmap = ResUtil.createQRImage(qrUrl, 670, 670, "1", centerLogo);
                if (mHandler != null) {
                    Message message = Message.obtain();
                    message.what = MSG_QRCODE_COMPLETE;
                    message.obj = qrImageBitmap;
                    mHandler.sendMessage(message);
                }
            }
        }).start();
    }

    private void showQrCodeSuccess(Bitmap bitmap) {
        Log.d(TAG, "showQrCodeSuccess ...");
        mBinding.rlLoading.setVisibility(View.GONE);
        mBinding.qrCodeLoadRetry.setVisibility(View.GONE);
        mBinding.qrCode.setImageBitmap(bitmap);
        mBinding.qrCode.setVisibility(View.VISIBLE);
        mBinding.bindCodePc.setVisibility(View.VISIBLE);
        String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG,"systemLanguage = "+systemLanguage);
        if (systemLanguage.contains("zh")){
            mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading_success));
        }else {
            mBinding.qrCodeLoading.setText("");
        }

        mBinding.bindCodeTx.setText(mCodePC);
        mBinding.snNumber.setVisibility(View.VISIBLE);
        mBinding.snNumberTx.setText(SystemUtils.getLast6SystemSerialNo());
    }

    private void showQrCodeFail() {
        Log.d(TAG, "showQrCodeFail ...");
        mBinding.rlLoading.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoadRetry.setVisibility(View.VISIBLE);
        mBinding.qrCode.setVisibility(View.VISIBLE);
        mBinding.qrCode.setImageResource(R.drawable.first_qrcode_inspire_image);
        mBinding.bindCodePc.setVisibility(View.GONE);
        mBinding.snNumber.setVisibility(View.GONE);
        mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading_fail));
        mBinding.qrCodeLoadRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
    }

    private void showLoading() {
        Log.d(TAG, "showLoading ...");
        mBinding.rlLoading.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoadRetry.setVisibility(View.GONE);
        mBinding.qrCodeLoading.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_loading));
        mBinding.qrCode.setVisibility(View.GONE);
        mBinding.bindCodePc.setVisibility(View.GONE);
        mBinding.snNumber.setVisibility(View.GONE);
    }

    private void showExpiresInUI() {
        Log.d(TAG, "showExpiresInUI ...");
        String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        mBinding.rlLoading.setVisibility(View.GONE);
        mBinding.bindCodePc.setVisibility(View.GONE);
        mBinding.snNumber.setVisibility(View.GONE);
        mBinding.qrCodeLoading.setVisibility(View.VISIBLE);
        mBinding.qrCode.setImageResource(R.drawable.first_qrcode_inspire_image);
        mBinding.qrCode.setVisibility(View.VISIBLE);
        mBinding.qrCodeLoadRetry.setVisibility(View.VISIBLE);

        if (systemLanguage.contains("zh")){
            mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again_des));
            mBinding.qrCodeLoadRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
        }else {
            mBinding.qrCodeLoading.setText(ResUtil.getString(R.string.binding_code_expired));
            mBinding.qrCodeLoadRetry.setText(ResUtil.getString(R.string.wx_qr_code_load_retry_again));
        }
    }


    class MyWeakHandler extends WeakHandler {

        public MyWeakHandler(Activity ref) {
            super(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (conflict()) {
                Log.e(TAG, "conflict");
                return;
            }
            switch (msg.what) {
                case MSG_QRCODE_COMPLETE:
                    Bitmap bitmap = (Bitmap) msg.obj;
                    showQrCodeSuccess(bitmap);
                    String systemLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
                    Log.d(TAG,"systemLanguage = "+systemLanguage);
                    if (!hasPlay && systemLanguage.contains("zh")) {
                        hasPlay = true;
                        SpeechManager.getInstance().speechPlayText(ResUtil.getString(R.string.wx_qr_code_tts));
                    }
                    break;
            }
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mHandler != null) {
            mHandler.removeMessages(MSG_QRCODE_COMPLETE);
        }
        hasPlay = false;
        Log.d(TAG, "QRcodeFragment onDestroy ");
    }

}
