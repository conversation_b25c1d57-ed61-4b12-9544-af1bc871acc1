/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.PreStateBean;
import com.ainirobot.firstconfig.bean.StateData;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.ainirobot.firstconfig.utils.ToastUtil;

public class StateMachine {
    private static final String TAG = StateMachine.class.getSimpleName() + ":FirstConfig";
    private static final Object sSyncLock = new Object();
    private static Looper sLooper;

    private StateHandler mStateHandler = null;

    private BaseState mState;

    private IStateListener mListener = new IStateListener() {
        @Override
        public void onSuccess() {
            startNext();
        }

        @Override
        public void onError(Object obj) {
            error(obj);
        }
    };

    static {
        HandlerThread handlerThread = new HandlerThread("StateMachine");
        handlerThread.start();
        sLooper = handlerThread.getLooper();
    }

    public StateMachine() {
        if (mStateHandler == null) {
            mStateHandler = new StateHandler(sLooper);
        }
        Log.d(TAG, "StateMachine init");
        mState = null;
    }

    public void start() {
        synchronized (sSyncLock) {
            if (mState == null) {
                if (TextUtils.isEmpty(SharedPrefUtil.getInstance().getConfigComplete())) {
                    transitionTo(new TransitionInfo(StateEnum.INIT.getState(), null));
                } else {
                    transitionTo(new TransitionInfo(StateEnum.INSPECT.getState(), null));
                }
            } else {
                Log.e(TAG, "cannot startInspect");
            }
        }
    }

    public void finishLanguageState() {
        Log.d(TAG, "finishLanguageState current mState : " + mState.getStateName());
        if (mState == StateEnum.LANGUAGE.getState()) {
            mState.onSuccess();
        }
    }

    /**
     * 返回前页
     */
    public void backPreState() {
        Log.d(TAG, "backPreState curState :" + mState.getStateName());
        transitionTo(new TransitionInfo(mState.getPreState(), new PreStateBean(true)));
    }

    public void refreshQrCode() {
        synchronized (sSyncLock) {
            if (mState == StateEnum.BINDING.getState()
                    && StateEnum.CONFIGURE.getState().isAvailable()) {
                transitionTo(new TransitionInfo(StateEnum.BINDING.getState(), null));
            } else {
                ToastUtil.showToast(FirstConfigApplication.getAppContext(),
                        ResUtil.getString(R.string.disconnect_dialog_content),
                        Toast.LENGTH_SHORT);
            }
        }
    }

    public void refreshSettingScenario() {
        synchronized (sSyncLock) {
            if (mState == StateEnum.SERVERLANG.getState()
                    || mState == StateEnum.SETTINGS_SCENARIO_LIST.getState()) {
                transitionTo(new TransitionInfo(StateEnum.SETTINGS_SCENARIO_LIST.getState(), null));
            } else {
                Log.e(TAG, "cannot start configure " + mState);
            }
        }
    }

    public void uploadSettingScenario(String platformId) {
        synchronized (sSyncLock) {
            if (mState == StateEnum.SETTINGS_SCENARIO_LIST.getState()
                    || mState == StateEnum.SETTINGS_SCENARIO_UPLOAD.getState()) {
                transitionTo(new TransitionInfo(StateEnum.SETTINGS_SCENARIO_UPLOAD.getState(), platformId));
            } else {
                Log.e(TAG, "cannot start configure " + mState);
            }
        }
    }

    public void setSettingsScenario(boolean skip) {
        synchronized (sSyncLock) {
            if (mState == StateEnum.SETTINGS_SCENARIO_LIST.getState()
                    || mState == StateEnum.SETTINGS_SCENARIO_UPLOAD.getState()
                    || mState == StateEnum.SETTINGS_SCENARIO_INFO.getState()) {
                transitionTo(new TransitionInfo(StateEnum.SETTINGS_SCENARIO_INFO.getState(), skip));
            } else {
                Log.e(TAG, "cannot start configure " + mState);
            }
        }
    }

    public void startChecking() {
        synchronized (sSyncLock) {
            if (mState == StateEnum.CONFIGURE.getState()) {
                if (StateEnum.CONFIGURE.getState().isAvailable()) {
                    transitionTo(new TransitionInfo(StateEnum.CHECKING.getState(), null));
                } else {
                    transitionTo(new TransitionInfo(StateEnum.CHECKING.getState(), null), 5 * 1000);
                }
            } else {
                Log.e(TAG, "cannot start checking");
            }
        }
    }

    public void startConfig() {
        synchronized (sSyncLock) {
            if (mState == StateEnum.LANGUAGE.getState()
                    && StateEnum.LANGUAGE.getState().isAvailable()) {
                transitionTo(new TransitionInfo(StateEnum.CONFIGURE.getState(), null));
            } else {
                Log.e(TAG, "cannot start configure " + mState);
            }
        }
    }

    private void startNext() {
        TransitionInfo transitionInfo = null;
        if (mState != null && mState.getNextState() != null) {
            transitionInfo = new TransitionInfo(mState.getNextState(), mState.getData());
        } else {
            transitionInfo = new TransitionInfo(StateEnum.ERROR.getState(),
                    new StateData(Definition.STATUS_ILLEGAL_STATE,
                            "[startNext]cur state is null",
                            RobotSettings.getSystemSn() + "-" + System.currentTimeMillis(),
                            null));
        }
        transitionTo(transitionInfo);
    }

    private void error(Object obj) {
        transitionTo(new TransitionInfo(StateEnum.ERROR.getState(), obj));
    }

    private void transitionTo(TransitionInfo transitionInfo) {
        transitionTo(transitionInfo, 0);
    }

    private void transitionTo(TransitionInfo transitionInfo, long delay) {
        Message msg = Message.obtain();
        msg.what = StateHandler.MSG_TRANSITION_TO;
        msg.obj = transitionInfo;

        if (mStateHandler.hasMessages(StateHandler.MSG_TRANSITION_TO)) {
            Log.d(TAG, "remove msg:" + StateHandler.MSG_TRANSITION_TO);
            mStateHandler.removeMessages(StateHandler.MSG_TRANSITION_TO);
        }

        if (delay > 0) {
            Log.e(TAG, "wait[" + delay + "]" + ", tranceTo:" + transitionInfo.getState());
            mStateHandler.sendMessageDelayed(msg, delay);
        } else {
            Log.e(TAG, "tranceTo:" + transitionInfo.getState());
            mStateHandler.sendMessage(msg);
        }
    }

    private void handleTransition(TransitionInfo transitionInfo) {
        BaseState state = transitionInfo.getState();
        if (state == null) {
            throw new IllegalStateException("State cannot be null");
        }

        Log.d(TAG, "handleTransition from[" + mState + "]" + " to [" + state + "]");
        if (mState != null) {
            Log.d(TAG, "cur state:" + mState.getStateName());
            mState.exit();
        }
        Log.d(TAG, "next state:" + state.getStateName());
        mState = state;
        state.preAction(transitionInfo.getData(), mListener);
        state.doAction();
    }

    private void handleRequest(MessageInfo obj) {
        if (mState != null) {
            Log.d(TAG, ((BaseState) mState).getStateName() + " handle msg:" + "");
//            mState.handleMessage(obj);
        }
    }

    private class StateHandler extends Handler {
        private static final int MSG_TRANSITION_TO = 100;
        private static final int MSG_NEW_REQUEST = 200;

        StateHandler(final Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(final Message msg) {
            switch (msg.what) {
                case MSG_TRANSITION_TO:
                    handleTransition((TransitionInfo) msg.obj);
                    break;
                case MSG_NEW_REQUEST:
                    handleRequest((MessageInfo) msg.obj);
                    break;
                default:
                    super.handleMessage(msg);
                    break;
            }
        }
    }

    private static class TransitionInfo {
        private final BaseState mState;
        private final Object mData;

        TransitionInfo(final BaseState state,
                       final Object data) {
            mState = state;
            mData = data;
        }

        BaseState getState() {
            return mState;
        }

        Object getData() {
            return mData;
        }

        @Override
        @NonNull
        public String toString() {
            return "TransitionInfo["
                    + ", mState="
                    + mState
                    + ", mData="
                    + mData
                    + ']';
        }
    }

    private static class MessageInfo {
        private final String mType;
        private final String mValue;

        MessageInfo(final String type,
                    final String value) {
            mType = type;
            mValue = value;
        }

        String getType() {
            return mType;
        }

        String getValue() {
            return mValue;
        }

        @Override
        @NonNull
        public String toString() {
            return "MessageInfo["
                    + ", mType="
                    + mType
                    + ", mValue="
                    + mValue
                    + ']';
        }
    }

    public BaseState getCurrentState() {
        return mState;
    }
}
