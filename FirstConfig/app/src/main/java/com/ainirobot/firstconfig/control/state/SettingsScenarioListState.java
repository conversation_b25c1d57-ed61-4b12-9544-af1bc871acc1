package com.ainirobot.firstconfig.control.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.SettingsScenarioList;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.google.gson.Gson;

import org.simple.eventbus.EventBus;

import java.util.List;

public class SettingsScenarioListState extends BaseState {

    public static final int RESPONSE_SUCCEED = 1;
    public static final int RESPONSE_SUCCEED_EMPTY = 2;
    public static final int RESPONSE_FAILED = 3;

    SettingsScenarioListState(int resId) {
        super(resId);
    }

    @Override
    protected void doAction() {
        requestSettingsScenarioList();
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.CONFIGURE.getState();
    }

    private void requestSettingsScenarioList() {
        SystemApi.getInstance().requestSettingsScenarioList(Definition.MODULE_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message, String extraData) {
                Log.d(getStateName(), "requestSettingsScenarioList : " + extraData);
                if (TextUtils.equals(message, Definition.SUCCEED)) {
                    try {
                        SettingsScenarioList paramsChoiceBeans = new Gson().fromJson(extraData, SettingsScenarioList.class);
                        List<SettingsScenarioList.SettingsScenarioListBean> scenarioList;
                        if (null != paramsChoiceBeans
                                && null != (scenarioList = paramsChoiceBeans.getSettingsScenarioList())) {
                            if (!scenarioList.isEmpty()) {
                                requestParamsResult(RESPONSE_SUCCEED, scenarioList);
                            } else {
                                requestParamsResult(RESPONSE_SUCCEED_EMPTY, null);
                            }
                        } else {
                            requestParamsResult(RESPONSE_FAILED, null);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        Log.d(getStateName(), "remoteQrcode parse error : " + e.getLocalizedMessage());
                        requestParamsResult(RESPONSE_FAILED, null);
                    }
                } else {
                    requestParamsResult(RESPONSE_FAILED, null);
                }
            }
        });
    }

    private void requestParamsResult(int type, List<SettingsScenarioList.SettingsScenarioListBean> datas) {
        EventBus.getDefault().post(new UIMessage<>(type, datas),
                FirstConfigDef.EVENTBUS_TAG_SETTINGS_SCENARIO_LIST);
    }
}
