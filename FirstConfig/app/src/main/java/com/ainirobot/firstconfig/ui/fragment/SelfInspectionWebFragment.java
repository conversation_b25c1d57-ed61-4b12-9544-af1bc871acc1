/*
 *
 *  *  Copyright (C) 2017 OrionStar Technology Project
 *  *
 *  *  Licensed under the Apache License, Version 2.0 (the "License");
 *  *  you may not use this file except in compliance with the License.
 *  *  You may obtain a copy of the License at
 *  *
 *  *       http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  *  Unless required by applicable law or agreed to in writing, software
 *  *  distributed under the License is distributed on an "AS IS" BASIS,
 *  *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  *  See the License for the specific language governing permissions and
 *  *  limitations under the License.
 *
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.drawable.AnimationDrawable;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.control.InspectionViewHelper;
import com.ainirobot.firstconfig.databinding.SelfInspectWebBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.SelfInspectWebViewModel;
import com.ainirobot.firstconfig.utils.WebUtil;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

/**
 * 招财海外版本，自检显示 WebView
 */
public class SelfInspectionWebFragment extends BaseFragment<SelfInspectWebViewModel, SelfInspectWebBinding> {

    private AnimationDrawable animationDrawable;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_self_inspection_web;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        WebUtil.hookWebView();
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (animationDrawable != null) {
            Log.d(TAG, "onViewCreated animationDrawable start");
            animationDrawable.start();
        }
    }

    @Override
    protected void initData() {
        if (isViewBound()) {
            Log.d(TAG, "initData ");
            animationDrawable = (AnimationDrawable) getActivity().getResources().getDrawable(R.drawable.anim_inspect_loading);
            mBinding.inspectingLoading.setImageDrawable(animationDrawable);
            mBinding.setClickListener(this);

            mBinding.buttNext.setClickable(true);
            mViewModel.getInspectFinish().observe(this, aBoolean -> {
                Log.d(TAG, "initData:getInspectFinish:observe:aBoolean=" + aBoolean);
                if (aBoolean) {
                    showInspectFinishUI();
                }
            });
            showWebView();
        }
    }

    @SuppressLint("ResourceAsColor")
    private void showWebView() {
        WebSettings webSettings = mBinding.webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        mBinding.webView.setWebViewClient(new WebViewClient() {
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                Log.d(TAG, "TestWeb:onReceivedError: errorCode=" + error.getErrorCode()
                        + " errorDescription=" + error.getDescription());
            }

            @Override
            public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
                super.onReceivedHttpError(view, request, errorResponse);
                Log.d(TAG, "TestWeb:onReceivedHttpError: statusCode="
                        + errorResponse.getStatusCode());
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                super.onReceivedSslError(view, handler, error);
                Log.d(TAG, "TestWeb:onReceivedSslError: error=" + error.toString());
            }
        });
        mBinding.webView.setBackgroundColor(R.color.black);
        mBinding.webView.loadUrl(InspectionViewHelper.getWebViewUrl());
    }

    private void showInspectFinishUI() {
        if (animationDrawable != null) {
            Log.d(TAG, "showInspectFinishUI: Stop loading animation!");
            animationDrawable.stop();
        }
        mBinding.inspectingLoading.setVisibility(View.INVISIBLE);
        mBinding.inspectTitle.setVisibility(View.INVISIBLE);
        Log.d(TAG, "showInspectFinishUI: Set buttNext clickable!");
        mBinding.buttNext.setEnabled(true);
    }
}
