package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.bi.ConfigLanguageReport;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.mvvm.model.LanguageDataHelper;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;

import org.simple.eventbus.EventBus;

import java.util.List;
import java.util.Locale;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

public class LanguageViewModel extends BaseViewModel<BaseModel> {

    private MutableLiveData<List<LanguageBean>> languageData = new MutableLiveData<>();
    private List<LanguageBean> allLanguageList;
    private String mDefaultCode = "";

    public LanguageViewModel(@NonNull Application application) {
        super(application);
    }

    public MutableLiveData<List<LanguageBean>> getLanguageData(){
        return languageData;
    }

    public void setDefaultCode(String newCode){
        if (TextUtils.isEmpty(newCode)){
            Log.e(TAG, "setDefaultCode failed , newCode is empty !");
            return;
        }
        this.mDefaultCode = newCode;
        SharedPrefUtil.getInstance().setFirstConfigLanguageCode(mDefaultCode);
    }

    @Override
    public void onStart() {
        super.onStart();

        allLanguageList = LanguageDataHelper.getInstance().getLocalLanguageList();
        if (allLanguageList.size() == 0) {
            Log.d(TAG, "onStart allLanguageList size 0 , register langObserver ");
            LanguageDataHelper.getInstance().registerListener(langObserver);
            return;
        }
        Log.d(TAG, "onCreate localLanguageList :"+allLanguageList);
        initLanguage();
    }

    private ContentObserver langObserver = new ContentObserver(null) {

        @Override
        public void onChange(boolean selfChange, Uri uri) {
            super.onChange(selfChange, uri);
            Log.d(TAG, "onChange uri :"+uri);
            allLanguageList = LanguageDataHelper.getInstance().getLocalLanguageList();
            initLanguage();
        }
    };

    private void initLanguage() {
        initDefaultLanguage();
        languageData.postValue(allLanguageList);
    }

    private void initDefaultLanguage() {
        // 只做首次默认，一旦用户修改了都以用户修改的为准,直到激活后再切换使用服务端的主语言.
        String localCode = SharedPrefUtil.getInstance().getFirstConfigLanguageCode();
        mDefaultCode = TextUtils.isEmpty(localCode) ? FirstConfigDef.DEFAULT_LANGUAGE : localCode;
        SharedPrefUtil.getInstance().setFirstConfigLanguageCode(mDefaultCode);
        Log.d(TAG, "initDefaultLanguage default robotSettingsLang :"+mDefaultCode);
        for (LanguageBean bean : allLanguageList){
            if (bean.getLangCode().equals(mDefaultCode)){
                bean.setIsDefault(LanguageBean.UseState.DEFAULT);
            }else {
                bean.setIsDefault(LanguageBean.UseState.NOT_DEFAULT);
            }
        }
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.butt_next:
                SystemApiProxy.getInstance().registerStatusListener(Definition.ROBOT_LANGUAGE, mStatusListener);
                SystemApiProxy.getInstance().setRobotString(Definition.ROBOT_LANGUAGE, mDefaultCode);//切换系统语言,仅在首次开机生效
                new ConfigLanguageReport().addLanguage(mDefaultCode).addWay().report();
                setLanguage(mDefaultCode);
                break;
            default:
                break;
        }
    }


    private StatusListener mStatusListener = new StatusListener(){

        @Override
        public void onStatusUpdate(String type, String data) throws RemoteException {
            super.onStatusUpdate(type, data);
            Log.d(TAG, "onStatusUpdate type :"+type+", data : "+data);
            if (Definition.ROBOT_LANGUAGE.equals(type)){
                next();
            }
        }
    };

    /**
     * 下一步，海外版本是选择云服务,国内版本是设置时区
     */
    private void next() {
        if (ProductInfo.isMeissa2() || ProductInfo.isMiniProduct()) {
            EventBus.getDefault().post(new UIMessage(
                            FirstConfigDef.OPEN_FRAGMENT_SET_OS_TYPE),
                    FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
        } else {
            EventBus.getDefault().post(new UIMessage(
                            ProductInfo.isOverSea() ? FirstConfigDef.OPEN_FRAGMENT_CLOUD_SERVER : FirstConfigDef.OPEN_FRAGMENT_SET_TIME_ZONE),
                    FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
        }
    }

    public static void setLanguage(String mLanguageCode) {
        if (TextUtils.isEmpty(mLanguageCode)) {
            return;
        }

        String[] info = mLanguageCode.split("_");
        Locale locale = new Locale(info[0], info[1]);
        Context context = FirstConfigApplication.getAppContext();
        Configuration configuration = context.getResources().getConfiguration();
        configuration.setLocale(locale);
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        context.getResources().updateConfiguration(configuration, displayMetrics);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        SystemApiProxy.getInstance().unregisterStatusListener(mStatusListener);
        LanguageDataHelper.getInstance().unRegisterListener(langObserver);
    }
}
