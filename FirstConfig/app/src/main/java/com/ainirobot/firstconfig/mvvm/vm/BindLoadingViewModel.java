package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.ServerLanguageBean;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;
import org.simple.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

public class BindLoadingViewModel extends BaseViewModel<BaseModel> {

    private MutableLiveData<ServerLanguageBean> languageState = new MutableLiveData<>();

    public MutableLiveData<ServerLanguageBean> getLanguageVerifyState() {
        return languageState;
    }

    public BindLoadingViewModel(@NonNull Application application) {
        super(application);
        EventBus.getDefault().register(this);
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_SERVER_LANGUAGE_VERIFY)
    public void onServerLanguageVerify(ServerLanguageBean result) {
        Log.d(TAG, "onServerLanguageVerify result : " + result);
        languageState.postValue(result);
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id){
            case R.id.iv_back:
                EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_BACK_PRE_PAGE);
                break;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
        Log.d(TAG, "BindLoadingViewModel onDestroy ");
    }
}
