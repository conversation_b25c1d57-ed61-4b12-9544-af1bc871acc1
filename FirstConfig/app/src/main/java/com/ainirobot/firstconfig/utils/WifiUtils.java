/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.telephony.CellInfo;
import android.telephony.CellInfoCdma;
import android.telephony.CellInfoGsm;
import android.telephony.CellInfoLte;
import android.telephony.CellInfoWcdma;
import android.telephony.CellSignalStrengthCdma;
import android.telephony.CellSignalStrengthGsm;
import android.telephony.CellSignalStrengthLte;
import android.telephony.CellSignalStrengthWcdma;
import android.telephony.PhoneStateListener;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;

import com.ainirobot.firstconfig.FirstConfigDef;

import java.util.List;
import java.util.Random;

import androidx.annotation.RequiresApi;

public class WifiUtils {
    private static final String TAG = WifiUtils.class.getSimpleName();
    private static final int TYPE_WIFI = 0;
    private static final int TYPE_UNKNOWN = 1;
    private static final int TYPE_2G = 2;
    private static final int TYPE_3G = 3;
    private static final int TYPE_4G = 4;
    private static final int TYPE_DISCONNECT = 99;

    /**
     * Network type is unknown
     */
    private static final int NETWORK_TYPE_UNKNOWN = 0;
    /**
     * Current network is GPRS
     */
    private static final int NETWORK_TYPE_GPRS = 1;
    /**
     * Current network is EDGE
     */
    private static final int NETWORK_TYPE_EDGE = 2;
    /**
     * Current network is UMTS
     */
    private static final int NETWORK_TYPE_UMTS = 3;
    /**
     * Current network is CDMA: Either IS95A or IS95B
     */
    private static final int NETWORK_TYPE_CDMA = 4;
    /**
     * Current network is EVDO revision 0
     */
    private static final int NETWORK_TYPE_EVDO_0 = 5;
    /**
     * Current network is EVDO revision A
     */
    private static final int NETWORK_TYPE_EVDO_A = 6;
    /**
     * Current network is 1xRTT
     */
    private static final int NETWORK_TYPE_1xRTT = 7;
    /**
     * Current network is HSDPA
     */
    private static final int NETWORK_TYPE_HSDPA = 8;
    /**
     * Current network is HSUPA
     */
    private static final int NETWORK_TYPE_HSUPA = 9;
    /**
     * Current network is HSPA
     */
    private static final int NETWORK_TYPE_HSPA = 10;
    /**
     * Current network is iDen
     */
    private static final int NETWORK_TYPE_IDEN = 11;
    /**
     * Current network is EVDO revision B
     */
    private static final int NETWORK_TYPE_EVDO_B = 12;
    /**
     * Current network is LTE
     */
    private static final int NETWORK_TYPE_LTE = 13;
    /**
     * Current network is eHRPD
     */
    private static final int NETWORK_TYPE_EHRPD = 14;
    /**
     * Current network is HSPA+
     */
    private static final int NETWORK_TYPE_HSPAP = 15;
    /**
     * Current network is GSM
     */
    private static final int NETWORK_TYPE_GSM = 16;
    /**
     * Current network is TD_SCDMA
     */
    private static final int NETWORK_TYPE_TD_SCDMA = 17;
    /**
     * Current network is IWLAN
     */
    private static final int NETWORK_TYPE_IWLAN = 18;
    /**
     * Current network is LTE_CA {@hide}
     */
    private static final int NETWORK_TYPE_LTE_CA = 19;


    private static final int GREAT_DBM = -89;
    private static final int GOOD_DBM = -97;
    private static final int NORMAL_DBM = -103;
    private static final int POOR_DBM = -140;

    public static String getWifiName(Context context) {
        try {
            WifiManager wifiMan = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = wifiMan.getConnectionInfo();
            if (wifiInfo == null)
                return FirstConfigDef.DEFAULT_SSID;
            String ssid = wifiInfo.getSSID();
            if (TextUtils.isEmpty(ssid))
                return FirstConfigDef.DEFAULT_SSID;
            return ssid.replace("\"", "");
        } catch (Exception e) {
            return FirstConfigDef.DEFAULT_SSID;
        }
    }

    /**
     * 判断是否已经链接到网络,并且可以访问网络
     *
     * @param c
     * @return
     */
    public static boolean isConnected(Context c) {
        ConnectivityManager cm = (ConnectivityManager) c.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
//            NetworkInfo[] infos = cm.getAllNetworkInfo();
//            if (infos != null) {
//                for (NetworkInfo ni : infos) {
//                    if (ni.isConnected()) {
//                        Log.d(TAG, "info : "+ni);
//                        if (needCheckNet){
//                            if (ni.isAvailable() && ni.getState() == NetworkInfo.State.CONNECTED)
//                                return true;
//                            return false;
//                        }
//                        return false;
//                    }
//                }
//            }

            //ainiRobot need mobile and wifi only.
            NetworkInfo info = cm.getActiveNetworkInfo();
            boolean internetValidated = true;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                NetworkCapabilities networkCapabilities = cm.getNetworkCapabilities(cm.getActiveNetwork());
                if (networkCapabilities != null) {
                    boolean internet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
                    boolean validated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);
                    Log.d(TAG, "internet : " + internet + ", validated = " + validated);
                    internetValidated = internet && validated;
                }
            }

            if (info != null) {
                if (info.getType() == ConnectivityManager.TYPE_MOBILE
                        || info.getType() == ConnectivityManager.TYPE_WIFI) {

                    return info.isAvailable() && info.isConnected() && internetValidated;
                }

            }
        }
        return false;
    }

    public static boolean isWifiConnected(Context c) {
        ConnectivityManager connecManager = (ConnectivityManager) c.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = null;
        try {
            networkInfo = connecManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        if (networkInfo != null) {
            return networkInfo.isConnected();
        } else {
            return false;
        }
    }


    public static int getConnectionType(Context c) {
        if (!isConnected(c))
            return TYPE_DISCONNECT;

        if (isWifiConnected(c))
            return TYPE_WIFI;

        TelephonyManager telephonyManager = (TelephonyManager) c.getApplicationContext().getSystemService(Context.TELEPHONY_SERVICE);
        if (telephonyManager != null) {
            int networkType = telephonyManager.getNetworkType();
            Log.d(TAG, "getConnectionType mobile type = " + networkType);
            switch (networkType) {
                case NETWORK_TYPE_GPRS:
                case NETWORK_TYPE_GSM:
                case NETWORK_TYPE_EDGE:
                case NETWORK_TYPE_CDMA:
                case NETWORK_TYPE_1xRTT:
                case NETWORK_TYPE_IDEN:
                    return TYPE_2G;
                case NETWORK_TYPE_UMTS:
                case NETWORK_TYPE_EVDO_0:
                case NETWORK_TYPE_EVDO_A:
                case NETWORK_TYPE_HSDPA:
                case NETWORK_TYPE_HSUPA:
                case NETWORK_TYPE_HSPA:
                case NETWORK_TYPE_EVDO_B:
                case NETWORK_TYPE_EHRPD:
                case NETWORK_TYPE_HSPAP:
                case NETWORK_TYPE_TD_SCDMA:
                    return TYPE_3G;
                case NETWORK_TYPE_LTE:
                case NETWORK_TYPE_LTE_CA:
                    return TYPE_4G;
                default:
                    return TYPE_UNKNOWN;
            }
        }
        return TYPE_UNKNOWN;
    }

    /**
     * get wifi signal strength
     *
     * @return signal level ,  max is (numLevel - 1)
     */
    public static int getWifiSignal(Context context) {
        WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        WifiInfo wifiInfo = mWifiManager.getConnectionInfo();
        if (wifiInfo == null) {
            Log.d(TAG, "getWifiSignal wifiInfo is null !!!");
            return 0;
        }
        return WifiManager.calculateSignalLevel(wifiInfo.getRssi(), 4);
    }

    public static int getDataSignal(TelephonyManager telephonyManager) {
        return 0;
    }

    private PhoneStateListener mPhoneStateListener = new PhoneStateListener() {

        @RequiresApi(api = Build.VERSION_CODES.M)
        @Override
        public void onSignalStrengthsChanged(SignalStrength signalStrength) {
            super.onSignalStrengthsChanged(signalStrength);
//            int level = signalStrength.getLevel();
            /** 这里注册监听后发现　level 一直是 5. */
        }
    };

    /**
     * 获取手机信号强度dBm
     *
     * @param telephonyManager
     * @return
     */
    public static int getMobileSignalDbm(TelephonyManager telephonyManager) {
        try {
            List<CellInfo> cellInfos = telephonyManager.getAllCellInfo();
            if (cellInfos != null) {
                for (CellInfo cellInfo : cellInfos) {
                    if (cellInfo instanceof CellInfoGsm) {
                        CellInfoGsm cellInfoGsm = (CellInfoGsm) cellInfo;
                        CellSignalStrengthGsm cellSignalStrengthGsm = cellInfoGsm.getCellSignalStrength();
                        int gsmDbm = cellSignalStrengthGsm.getDbm();
                        Log.d(TAG, "signal_strength, gsm_dbm : " + gsmDbm);
                        if (gsmDbm == Integer.MAX_VALUE)
                            return -140;
                        else
                            return gsmDbm;
                    } else if (cellInfo instanceof CellInfoWcdma) {
                        CellInfoWcdma cellInfoWcdma = (CellInfoWcdma) cellInfo;
                        CellSignalStrengthWcdma cellSignalStrengthWcdma = cellInfoWcdma.getCellSignalStrength();
                        int wcdmaDbm = cellSignalStrengthWcdma.getDbm();
                        Log.d(TAG, "wcdma_dbm : " + wcdmaDbm);
                        if (wcdmaDbm == Integer.MAX_VALUE)
                            return -140;
                        else
                            return wcdmaDbm;
                    } else if (cellInfo instanceof CellInfoLte) {
                        CellInfoLte cellInfoLte = (CellInfoLte) cellInfo;
                        CellSignalStrengthLte cellSignalStrengthLte = cellInfoLte.getCellSignalStrength();
                        int lteDbm = cellSignalStrengthLte.getDbm();
                        int lteCalDbm = getTransLteSignal(lteDbm);
                        Log.d(TAG, "lte_dbm : " + lteCalDbm);
                        return lteCalDbm;
                    } else if (cellInfo instanceof CellInfoCdma) {
                        CellInfoCdma cellInfoCdma = (CellInfoCdma) cellInfo;
                        CellSignalStrengthCdma cellSignalStrengthCdma = cellInfoCdma.getCellSignalStrength();
                        int cdmaDbm = cellSignalStrengthCdma.getCdmaDbm();
                        int evdoDbm = cellSignalStrengthCdma.getEvdoDbm();
                        Log.d(TAG, "cdma_dbm, cdm :" + cdmaDbm + ", evdo : " + evdoDbm);
                        if (cdmaDbm < evdoDbm)
                            return getTransCdmaSignal(cdmaDbm);
                        else
                            return getTransEvdoSignal(evdoDbm);
                    }
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return -140;
    }


    private static int getTransCdmaSignal(int cdmaDbm) {
        int dbm = -108;
        if (cdmaDbm >= -75) {
            dbm = GREAT_DBM + (cdmaDbm + 75) * 39 / 25;
        } else if (cdmaDbm >= -85) {
            dbm = GOOD_DBM + (cdmaDbm + 85) * (GREAT_DBM - GOOD_DBM) / 10;
        } else if (cdmaDbm >= -95) {
            dbm = NORMAL_DBM + (cdmaDbm + 95) * (GOOD_DBM - NORMAL_DBM) / 10;
        } else {
            dbm = POOR_DBM + (cdmaDbm + 110) * (NORMAL_DBM - POOR_DBM) / 15;
        }
        Log.d(TAG, "cdma_dbm==>cdm==" + cdmaDbm);
        if (dbm >= 0) {
            Random random = new Random();
            dbm = -50 + random.nextInt(10) + 1;
        }
        return dbm;
    }

    private static int getTransLteSignal(int lteDbm) {
        int dbm = -108;
        if (lteDbm >= -95) {
            dbm = GREAT_DBM + (lteDbm + 95) * 39 / 45;
        } else if (lteDbm >= -105) {
            dbm = GOOD_DBM + (lteDbm + 105) * (GREAT_DBM - GOOD_DBM) / 10;
        } else if (lteDbm >= -115) {
            dbm = NORMAL_DBM + (lteDbm + 115) * (GOOD_DBM - NORMAL_DBM) / 10;
        } else {
            dbm = POOR_DBM + (lteDbm + 140) * (NORMAL_DBM - POOR_DBM) / 25;
        }
        if (dbm >= 0) {
            Random random = new Random();
            dbm = -50 + random.nextInt(10) + 1;
        }
        return dbm;
    }

    private static int getTransEvdoSignal(int evdoDbm) {
        int dbm = -108;
        if (evdoDbm >= -65) {
            dbm = GREAT_DBM + (evdoDbm + 65) * 39 / 15;
        } else if (evdoDbm >= -75) {
            dbm = GOOD_DBM + (evdoDbm + 75) * (GREAT_DBM - GOOD_DBM) / 10;
        } else if (evdoDbm >= -90) {
            dbm = NORMAL_DBM + (evdoDbm + 90) * (GOOD_DBM - NORMAL_DBM) / 15;
        } else {
            dbm = POOR_DBM + (evdoDbm + 110) * (NORMAL_DBM - POOR_DBM) / 20;
        }
        Log.d(TAG, "cdma_dbm==>evdo==" + evdoDbm);
        if (dbm >= 0) {
            Random random = new Random();
            dbm = -50 + random.nextInt(10) + 1;
        }
        return dbm;
    }

    /**
     * you must differ ImageAsset and  VectorAsset  ,  drawable to bitmap ;
     *
     * @param drawable
     * @return
     */
    public static Bitmap drawbleToBitmap(Drawable drawable) {
        Bitmap bitmap = null;
        if (drawable instanceof BitmapDrawable) {        // 位图
            bitmap = ((BitmapDrawable) drawable).getBitmap();
        } else {                // 矢量图
            Bitmap bm = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bm);
            drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            drawable.draw(canvas);
            bitmap = bm;
        }
        return bitmap;

    }

    /**
     * hide system keyboard
     */
    public static void hideKeyboard(View view) {
        InputMethodManager imm = (InputMethodManager) view.getContext().getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }

    /**
     * show system keyboard
     */
    public static void showInputMethod(View view) {
        InputMethodManager imm = (InputMethodManager) view.getContext().getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
            imm.showSoftInput(view, 0);
        }
    }

    /**
     * @param curSSID
     * @return
     */
    public static boolean isContainsCheckedFreeWifi(String curSSID) {
        boolean b = false;
//        String checkedFreeWifi = SharedPrefsUtil.getIns().getAllCheckedFreeWifi();
//        CommonLog.dd("isContainsCheckedFreeWifi  cheeckFreeWifi = "+checkedFreeWifi);
//        if(!TextUtils.isEmpty(checkedFreeWifi)){
//            String[] wifis  = checkedFreeWifi.split("\"\"");
//            for(int i = 0; i<wifis.length ; i++){
//                if(wifis[i].equals(curSSID)){
//                    b = true;
//                    break;
//                }
//            }
//        }
        return b;
    }


    /**
     * cycle ImageView memory;
     *
     * @param view
     */
    public static void recycleImageView(View view) {
        if (view == null) return;
        if (view instanceof ImageView) {
            Drawable drawable = ((ImageView) view).getDrawable();
            if (drawable instanceof BitmapDrawable) {
                Bitmap bmp = ((BitmapDrawable) drawable).getBitmap();
                if (bmp != null && !bmp.isRecycled()) {
                    ((ImageView) view).setImageBitmap(null);
                    bmp.recycle();
                    bmp = null;
                }
            }
        }
    }


}
