package com.ainirobot.firstconfig;

import android.util.Log;

/**
 * Created by Orion on 2022/2/18.
 */

public class GlobalData {
    private static final String TAG = "FirstConfig_GlobalData";

    public static final GlobalData INSTANCE = new GlobalData();

    private GlobalData() {
    }

    private volatile boolean mShowWebViewInspectUI = false;

    public boolean isShowWebViewInspectUI() {
        return this.mShowWebViewInspectUI;
    }

    public void setShowWebViewInspectUI(boolean mShowWebViewInspectUI) {
        Log.d(TAG, "setShowWebViewInspectUI=" + mShowWebViewInspectUI);
        this.mShowWebViewInspectUI = mShowWebViewInspectUI;
    }
}
