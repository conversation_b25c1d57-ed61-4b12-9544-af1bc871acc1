package com.ainirobot.firstconfig.bean;

public class AreaBean {
    private String country_code;
    private String country_name;
    private boolean isDefault; // true表示 默认是选中的

    public AreaBean(String country_code, String country_name, boolean isDefault) {
        this.country_code = country_code;
        this.country_name = country_name;
        this.isDefault = isDefault;
    }

    public String getCountryCode() {
        return country_code;
    }

    public void setCountryCode(String country_code) {
        this.country_code = country_code;
    }

    public String getCountryName() {
        return country_name;
    }

    public void setCountryName(String country_name) {
        this.country_name = country_name;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }


    @Override
    public String toString() {
        return "Country{" +
                "country_code='" + country_code + '\'' +
                ", country_name='" + country_name + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }

}
