/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.firstconfig.ui.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.CloudServerBean;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.utils.CloudUtils;
import com.ainirobot.firstconfig.utils.ResUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.appcompat.app.AlertDialog;
import androidx.constraintlayout.widget.ConstraintLayout;

public class CloudDialog extends AlertDialog implements View.OnClickListener{
    private static final String TAG = CloudDialog.class.getSimpleName();

    private Context mContext;
    private TextView mConfirm, mCancel;
    private ConstraintLayout mLayout;
    private ListView mServerList;
    private TextView mContent;
    private MyAdapter mAdapter;
    private String mServerCode;
    private List<CloudServerBean> mList = new ArrayList<>();
    private boolean serverSwitched = false;


    public CloudDialog(Context context) {
        this(context,0);
        mContext = context;
    }

    public CloudDialog(Context context, int theme) {
        super(context, theme);
        mContext = context;
    }

    private ClickListener mClickListener ;
    public interface ClickListener{
        void onConfirmClick();
        void onCancelClick();
    }

    public CloudDialog setDialogClickListener(ClickListener listener){
        this.mClickListener = listener;
        return this;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_dialog);
        WindowManager.LayoutParams p = this.getWindow().getAttributes();
        p.width = 950;
        p.height = 980;
        this.getWindow().setAttributes(p);
        setCanceledOnTouchOutside(false);

        initView();
    }

    private void initView() {
        mLayout = (ConstraintLayout)findViewById(R.id.dialog_parent);
        mContent = (TextView)findViewById(R.id.content);
        mConfirm = (TextView) findViewById(R.id.confirm);
        mCancel = (TextView) findViewById(R.id.cancel);
        mConfirm.setOnClickListener(this);
        mCancel.setOnClickListener(this);
//        ViewOutlineProvider viewOutlineProvider = new ViewOutlineProvider() {
//            @Override
//            public void getOutline(View view, Outline outline) {
//                view.setClipToOutline(true);
//                outline.setRoundRect(0,0,view.getWidth(),view.getHeight(),30);
//            }
//        };
//        mLayout.setOutlineProvider(viewOutlineProvider);
        mServerList = (ListView)findViewById(R.id.lv_types);
        mAdapter = new MyAdapter(mContext);
    }

    private boolean isServerSwitched(String newCode) {
        String localDefaultCode = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "isServerSwitched currentServerCode : " + localDefaultCode + " newServerCode：" + newCode);
        //根据服务器区域名称，判断是否被选中
        return CloudUtils.isServerSwitched(newCode, localDefaultCode);
    }

    @Override
    public void show() {
        ObjectAnimator.ofFloat(mLayout,"alpha",0,1)
                .setDuration(170)
                .start();
        super.show();

        mContent.setVisibility(View.GONE);
        Log.d(TAG, "show Cloud Dialog");
        updateServerList();
    }

    private void updateServerList() {
        mServerList.setAdapter(mAdapter);
        mServerList.setOnItemClickListener((parent, view, position, id) -> {
            CloudServerBean cloudServerBean = (CloudServerBean) mAdapter.getItem(position);
            String serverId = cloudServerBean.getServer_id();
            itemClick(position);
            serverSwitched = isServerSwitched(serverId);
            mConfirm.setText(serverSwitched ? ResUtil.getString(R.string.cloud_reboot) : ResUtil.getString(R.string.cloud_select_ok));
            mServerCode = serverId;
        });
        mAdapter.updateData(initCloudServers());
        mAdapter.notifyDataSetChanged();
    }

    private void itemClick(int clickPosition) {
        int count = mAdapter.getCount();
        for (int i = 0; i < count; i++) {
            ((CloudServerBean) mAdapter.getItem(i)).setDefault(i == clickPosition);
        }
        mAdapter.notifyDataSetChanged();
    }

    private List<CloudServerBean> initCloudServers() {
        String configCloud = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        if (TextUtils.isEmpty(configCloud)){
            configCloud = Definition.CloudServerZone.DEFAULT.getValue();
        }
        mServerCode = SystemApiProxy.getInstance().getRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE);
        Log.d(TAG, "initCloudServers localServerCode: " + mServerCode + ", systemServer :" + configCloud);
        mList.clear();
        mList.addAll(CloudUtils.getAllCloudServerBean(configCloud, mServerCode));
        return mList;
    }

    @Override
    public void onClick(View v) {
        int vId = v.getId();
        switch (vId){
            case R.id.confirm:
                SystemApiProxy.getInstance().setRobotString(Definition.ROBOT_SETTING_CLOUD_SERVER_CODE, mServerCode);
                if (serverSwitched){
                    SystemApiProxy.getInstance().setRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE, CloudUtils.getServerZone(mServerCode));
                    reboot();
                }
                if (mClickListener != null){
                    mClickListener.onConfirmClick();
                }
                dismiss();
                break;
            case R.id.cancel:
                if (mClickListener != null){
                    mClickListener.onCancelClick();
                }
                break;
            default:
                Log.d(TAG, "default case");
                break;

        }
    }

    private void reboot(){
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        FirstConfigApplication.getAppContext().startActivity(intent);
    }

    class MyAdapter extends BaseAdapter {

        private LayoutInflater mInflater;
        private List<CloudServerBean> mData = new ArrayList<>();

        public MyAdapter(Context mContext) {
            mInflater = LayoutInflater.from(mContext);
        }

        public List<CloudServerBean> getData() {
            return mData;
        }

        public void updateData(List<CloudServerBean> list) {
            mData.clear();
            mData.addAll(list);
        }

        @Override
        public int getCount() {
            return mData.size();
        }

        @Override
        public Object getItem(int position) {
            return mData.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = null;
            if (convertView == null) {
                holder = new ViewHolder();
                convertView = mInflater.inflate(R.layout.item_lv_language, null);
                holder.tvLanguage = (TextView) convertView.findViewById(R.id.tv_language);
                holder.imSelect = (ImageView) convertView.findViewById(R.id.im_select);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }
            CloudServerBean bean = (CloudServerBean) mData.get(position);
            holder.tvLanguage.setText(bean.getServer_name());
            holder.tvLanguage.setTextColor(Color.BLACK);
            holder.imSelect.setImageResource(bean.isDefault() ? R.drawable.select : R.drawable.unselect);
            return convertView;
        }

        class ViewHolder {
            TextView tvLanguage;
            ImageView imSelect;
        }
    }
}
