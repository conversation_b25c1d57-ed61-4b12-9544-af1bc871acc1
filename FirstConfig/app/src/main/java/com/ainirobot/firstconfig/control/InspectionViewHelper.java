package com.ainirobot.firstconfig.control;

import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.firstconfig.GlobalData;
import com.ainirobot.firstconfig.control.state.StateEnum;
import com.ainirobot.firstconfig.control.state.StateMachine;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Locale;

/**
 * Created by Orion on 2022/2/18.
 */

public class InspectionViewHelper {
    private static final String TAG = "InspectionViewHelper";

    private static final int CHECK_URL_TIME_LIMIT = 5 * 1000;
    private static final int CHECK_TIME_LIMIT = 1;

    private static String sWebViewUrl = "https://global-kb.orionstar.com/public/FirstConfigVideo/Lucki/?lan=en_US";
    private static volatile boolean sChecking = false;

    public static String getWebViewUrl() {
        return sWebViewUrl;
    }

    /**
     * 海外招财版本默认显示 webView 自检页面
     */
    public static void checkIsNeedShowWebView(StateMachine stateMachine) {
        Log.d(TAG, "checkIsNeedShowWebView:sChecking=" + sChecking);
        buildWebViewUrl();
        if (sChecking) {
            return;
        }
        if (!ProductInfo.isDeliveryOverSea()) {
            GlobalData.INSTANCE.setShowWebViewInspectUI(false);
            return;
        }
        sChecking = true;
        HandlerThread checkUrl = new HandlerThread("CheckUrl");
        checkUrl.start();
        new Handler(checkUrl.getLooper()).post(new Runnable() {
            @Override
            public void run() {
                boolean urlState = checkUrl(sWebViewUrl, CHECK_URL_TIME_LIMIT);
                Log.d(TAG, "checkIsNeedShowWebView: urlState=" + urlState);
                if (stateMachine != null && stateMachine.getCurrentState() != null
                        && stateMachine.getCurrentState() != StateEnum.INSPECT.getState()) {
                    Log.d(TAG, "checkIsNeedShowWebView:  Do Set!");
                    GlobalData.INSTANCE.setShowWebViewInspectUI(urlState);
                }
                sChecking = false;
            }
        });
    }

    private static void buildWebViewUrl() {
        String language = Locale.getDefault().toString();
        Log.d(TAG, "buildWebViewUrl: language=" + language);
        sWebViewUrl = sWebViewUrl.replace("en_US", language);
        Log.d(TAG, "buildWebViewUrl: sWebViewUrl=" + sWebViewUrl);
    }

    private static Boolean checkUrl(String address, int waitMilliSecond) {
        Log.d(TAG, "checkUrl: thread=" + Thread.currentThread());
        try {
            URL url = new URL(address);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setUseCaches(false);
            conn.setInstanceFollowRedirects(true);
            conn.setConnectTimeout(waitMilliSecond);
            conn.setReadTimeout(waitMilliSecond);

            //HTTP connect
            long startConnect;
            try {
                startConnect = System.currentTimeMillis();
                Log.d(TAG, "checkUrl: Start connect!");
                conn.connect();
            } catch (Exception e) {
                Log.d(TAG, "checkUrl: Connect e=" + e.toString());
                e.printStackTrace();
                return false;
            }

            int code = conn.getResponseCode();
            Log.d(TAG, "checkUrl: Response code=" + code + " Cost time:" + (System.currentTimeMillis() - startConnect) + "(ms)");
            if ((code >= 100) && (code < 400)) {
                return true;
            }

            return false;
        } catch (Exception e) {
            Log.d(TAG, "checkUrl: Check e=" + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

}
