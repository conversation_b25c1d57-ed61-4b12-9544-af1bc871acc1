/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.text.Html;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.TextListener;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.control.SpeechManager;
import com.ainirobot.firstconfig.databinding.BindSuccessBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.BindSuccessViewModel;
import com.ainirobot.firstconfig.ui.activity.FirstConfigActivity;
import com.ainirobot.firstconfig.ui.view.WoowaDialog;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;

/**
 * 机器人端和服务端通过二维码绑定成功页
 */
public class BindSuccessFragment extends BaseFragment<BindSuccessViewModel, BindSuccessBinding> {

    private String robotName;
    private int mTtsCount = 0;
    private WoowaDialog mDialog;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_bind_success;
    }

    @Override
    protected void initData() {
        mBinding.setClickListener(this);
        robotName = RobotSettings.getRobotName(this.getContext());
        mViewModel.getRobotNameState().observe(this, name -> {
            updateName(name);
        });
        mBinding.bingSuccessRobotName.setText(robotName);

        Log.d(TAG, "BindSuccessFragment onCreateView robotName : " + robotName);
    }

    private void updateName(String robotName) {
        Log.d(TAG, "updateName :" + robotName);
        this.robotName = robotName;
        mBinding.bingSuccessRobotName.setText(robotName);
    }


    @Override
    public void onStart() {
        super.onStart();
        Log.d(TAG, "BindSuccessFragment onStart ");


        boolean isNavigationEnable;
        try {
            isNavigationEnable = SystemApi.getInstance().isNavigationServiceEnable();
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "Get navigation service status failed : " + SystemUtils.hasMapTools());
            isNavigationEnable = SystemUtils.hasMapTools();
        }

        //导航服务可用提示创建地图，否则直接退出首次配置
        if (isNavigationEnable) {
            mHandler.sendEmptyMessage(MSG_NO_MAP);
        } else {
            mHandler.sendEmptyMessage(MSG_FINISH);
        }
        String timezone = RobotSettings.getSysTimeZone();
        Log.d(TAG, "timezone : " + timezone);
        if (WoowaDialog.TimeZoneConstant.Timezone.SEOUL.equalsIgnoreCase(timezone)) {
            showWoowaDialog();
        }
    }

    private static final int MSG_NO_MAP = 0x001;
    private static final int MSG_HAS_MAP = 0x002;
    private static final int MSG_FINISH = 0x003;

    private final Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            FirstConfigActivity activity = (FirstConfigActivity) getActivity();
            if (activity == null) {
                return;
            }
            switch (msg.what) {
                case MSG_NO_MAP:
                    mBinding.bottomBtn.setText(ResUtil.getString(R.string.bind_success_create_map));
                    boolean speechOnce = checkSpeechOnce();
                    Log.d(TAG, "speechOnce : " + speechOnce);
                    if (speechOnce) { // 只播报一次.
                        String currentLanguage = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
                        Log.d(TAG, "handleMessage currentLanguage : " + currentLanguage);
                        SpeechManager.getInstance().speechPlayText(ResUtil.getString(R.string.bind_tts, robotName));
                    }
                    break;
                case MSG_HAS_MAP:
                    mBinding.bottomBtn.setText(ResUtil.getString(R.string.bind_success_has_map));
                    SpeechManager.getInstance().speechPlayText(ResUtil.getString(R.string.bind_tts_1, robotName));
                    break;

                case MSG_FINISH:
                    finish();
                    break;

                default:
                    Log.d(TAG, "default case");
                    break;
            }
        }
    };

    private void finish() {
        mBinding.bottomBtn.setVisibility(View.GONE);
        mBinding.bottomBtnDes.setVisibility(View.GONE);

        delayFinish(10 * Definition.SECOND);

        String ttsText = getString(R.string.bind_qr_code_tts_vase, robotName);
        SpeechManager.getInstance().speechPlayText(ttsText, new TextListener() {

            @Override
            public void onStop() {
                delayFinish(2 * Definition.SECOND);
            }

            @Override
            public void onError() {
                delayFinish(2 * Definition.SECOND);
            }

            @Override
            public void onComplete() {
                delayFinish(2 * Definition.SECOND);
            }
        });
    }

    private void delayFinish(long time) {
        String tag = "delayFinish";

        Log.d(TAG, "Start delay finish task ");

        DelayTask.cancel(tag);
        DelayTask.submit(tag, new Runnable() {
            @Override
            public void run() {
                mViewModel.endFirstConfig(Definition.FIRST_CONFIG_END_TO_DEFAULT);
            }
        }, time);
    }

    private boolean checkSpeechOnce() {
        if (mTtsCount == 0) {
            mTtsCount++;
            return true;
        } else {
            mTtsCount++;
            return false;
        }
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        cancelDialog();
        mHandler.removeMessages(MSG_HAS_MAP);
        mHandler.removeMessages(MSG_NO_MAP);
        mTtsCount = 0;
    }

    private void showWoowaDialog() {
        if (mDialog != null && mDialog.isShowing()) {
            return;
        }
        mDialog = new WoowaDialog(getActivity());
        mDialog.show();
        String content = "<font color = '#D31812'> " + ResUtil.getString(R.string.enable_one_time_password) + " </font>";
        String content1 = "<font color = '#000000'> " + ResUtil.getString(R.string.be_careful) + " </font>";
        mDialog.setContent(content + content1);
    }

    private void cancelDialog() {
        if (mDialog != null && mDialog.isShowing()) {
            mDialog.dismiss();
            mDialog = null;
        }
    }
}