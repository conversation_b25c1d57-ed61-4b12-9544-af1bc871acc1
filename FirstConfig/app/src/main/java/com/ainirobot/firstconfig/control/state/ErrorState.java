/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.StateData;
import com.ainirobot.firstconfig.bean.UIMessage;

import org.simple.eventbus.EventBus;

public class ErrorState extends BaseState {
    private static final String TAG = ErrorState.class.getSimpleName();

    private Object mData;

    ErrorState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);

        mData = data;

        if (mData instanceof StateData) {
            Log.e(TAG, "id:" + ((StateData) mData).id + ", msg:" + ((StateData) mData).msg);
        }
    }

    @Override
    protected void doAction() {
        //TODO:zhujie
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SHOW_INNERERROR, mData),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

    @Override
    protected BaseState getNextState() {
        //TODO:zhujie
        return null;
    }

    @Override
    protected void exit() {
        super.exit();
    }
}
