package com.ainirobot.firstconfig.utils;

import android.content.res.Configuration;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.R;

public enum ResType {
    /**
     * 绑定成功后选择运行场景
     */
    SETTINGS_SCENARIO_ICON {

        @Override
        public int getSaiphProRes() {
            return ProductInfo.isAlnilamPro() ? R.drawable.settings_scenario_autodoor : R.drawable.settings_scenario_failed_mini;
        }

        @Override
        public int getSlimRes() {
            return ProductInfo.isSlimDoor() ? R.drawable.settings_scenario_dslim : R.drawable.settings_scenario_slim;
        }

        @Override
        public int getLandScapeRes() {
            return R.drawable.settings_scenario_failed_mini;
        }

        @Override
        public int getDefaultRes() {
            return R.drawable.settings_scenario_failed_saiph;
        }
    };

    /**
     * mini资源
     */
    public int getMiniRes() {
        return getLandScapeRes();
    }

    /**
     * CarryBot资源
     */
    public int getCarryRes() {
        return getLandScapeRes();
    }

    public int getSlimRes() {
        return getLandScapeRes();
    }

    /**
     * 小秘2资源，默认使用横屏资源
     */
    public int getMeissa2Res() {
        return getLandScapeRes();
    }
    /**
     * 招财豹pro资源
     */
    public int getSaiphProRes() {
        return getLandScapeRes();
    }

    /**
     * 横屏资源,默认使用default资源
     */
    public int getLandScapeRes() {
        return getDefaultRes();
    }

    /**
     * 递送项目资源，默认使用default资源
     */
    public int getDeliveryRes() {
        return getDefaultRes();
    }

    /**
     * 小秘Plus项目资源，默认使用default资源
     */
    public int getMeissaPlusRes() {
        return getDefaultRes();
    }

    /**
     * default资源
     */
    public abstract int getDefaultRes();

    /**
     * 是否为横屏
     */
    public static boolean isLandScape() {
        Configuration configuration = FirstConfigApplication.getAppContext().getResources().getConfiguration();
        return configuration.orientation == Configuration.ORIENTATION_LANDSCAPE;
    }

    private boolean isLandScape = isLandScape();

    /**
     * 根据类型，获得适合当前设备的资源
     *
     * @return 符合当前设备类型的资源id
     */
    public int getResIdByType() {
        //Slim
        if(ProductInfo.isSlimProduct()){
            return getSlimRes();
        }
        //Carry项目
        if(ProductInfo.isCarryProduct()){
            return getCarryRes();
        }

        //招财豹pro项目
        if (ProductInfo.isSaiphPro() || ProductInfo.isAlnilamPro()) {
            return getSaiphProRes();
        }

        //mini项目
        if (ProductInfo.isMiniProduct()) {
            return getMiniRes();
        }

        //小秘2
        if (ProductInfo.isMeissa2()) {
            return getMeissa2Res();
        }

        if (ProductInfo.isMeissaPlus()) {
            return getMeissaPlusRes();
        }

        //横屏使用资源
        if (isLandScape) {
            return getLandScapeRes();
        }

        //递送项目资源
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus()) {
            return getDeliveryRes();
        }

        return getDefaultRes();
    }
}