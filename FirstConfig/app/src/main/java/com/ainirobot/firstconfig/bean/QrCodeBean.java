package com.ainirobot.firstconfig.bean;

import com.ainirobot.coreservice.client.Definition;
import com.google.gson.annotations.SerializedName;

public class QrCodeBean {
    @SerializedName(Definition.QRCODE_DATA)
    private String mData;
    @SerializedName(Definition.EXPIRES_IN)
    private long mExpiresInTime; // unit is second
    @SerializedName(Definition.BIND_CODE)
    private String mBindCode; //bindCode for server,for example:"617034"

    public String getData() {
        return mData;
    }
    public long getExspireTime() {
        return mExpiresInTime;
    }

    public String getBindCode() {
        return mBindCode;
    }

    @Override
    public String toString() {
        return "QrCodeBean{" +
                "mData='" + mData +
                ", mExpiresInTime=" + mExpiresInTime +
                ", mBindCode=" + mBindCode +
                '}';
    }
}
