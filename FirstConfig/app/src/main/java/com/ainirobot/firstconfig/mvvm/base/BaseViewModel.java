package com.ainirobot.firstconfig.mvvm.base;

import android.app.Application;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.mvvm.util.ReflexUtil;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;

public class BaseViewModel<M extends BaseModel> extends AndroidViewModel implements LifecycleObserver {

    protected M model;
    private FirstConfigApplication mFCApplication;
    protected final String TAG = FirstConfigDef.FLAG + getClass().getSimpleName();

    public BaseViewModel(@NonNull Application application) {
        super(application);
        mFCApplication = (FirstConfigApplication) application;
        ReflexUtil<M> mReflexUtil = new ReflexUtil<>();
        model = mReflexUtil.getObj(getClass());
    }

//    protected void switchFragment(Fragment fragment) {
//        Log.i(TAG, "switchFragment: " + fragment);
//
//        LiveEventBus.get(SWITCH_FRAGMENT).post(fragment);
//        onFinish();
//    }
//
//    protected void onFinish(){
//        Log.d(TAG, "onFinish ");
//    }

    public FirstConfigApplication getApplication(){
        return mFCApplication;
    }

    public M getModel() {
        return model;
    }

    protected void viewClick(@IdRes int id) {
        Log.i(TAG, "BaseViewModel viewClick: ");
    }


    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    public void onCreate() {
        Log.i(TAG, "BaseViewModel onCreate: ");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    public void onStart() {
        Log.i(TAG, "BaseViewModel onStart: ");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    public void onResume() {
        Log.i(TAG, "BaseViewModel onResume: ");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    public void onPause() {
        Log.i(TAG, "BaseViewModel onPause: ");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    public void onStop() {
        Log.i(TAG, "BaseViewModel onStop: ");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void onDestroy() {
        Log.i(TAG, "BaseViewModel onDestroy: ");
    }
}
