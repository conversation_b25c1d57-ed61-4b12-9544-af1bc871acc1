/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig;

import com.ainirobot.coreservice.client.ProductInfo;

public class FirstConfigDef {

    public static final String FLAG = "FC_";
    public static final String DEFAULT_LANGUAGE =
            ProductInfo.isMiniProduct()|| ProductInfo.isOverSea() ? "en_US" : "zh_CN";// 英文
    public static final String LANGUAGE_CODE_EN = "en_US";// 英文
    public static final String LANGUAGE_CODE_JA = "ja_JP";// 日文
    public static final String LANGUAGE_CODE_KO = "ko_KR";// 韩文

    public static final String BUNDLE_ID = "bundle_id";
    public static final String BUNDLE_INTENT = "bundle_intent";
    public static final String BUNDLE_PARAM = "bundle_param";

    public static final String BUNDLE_BIND_STATE = "bundle_bind_state";
    public static final String BUNDLE_IS_STOP_MAP = "bundle_is_from_stop_map";
    public static final String BUNDLE_IS_LOGIN_ERROR = "bundle_is_login_error";
    public static final String BUNDLE_LANGUAGE_CODE = "bundle_language_code";
    public static final String FIRST_ACTIVATION = "first_activation";
    public static final int FIRST_ACTIVATION_VALUE = 1;

    public static final int MSG_REPOSITION_GUIDE_CONFIRM = 118;
    public static final int MSG_REPOSITION_FAILURE_RETRY = 119;
    public static final int MSG_REPOSITION_FAILURE_SKIP = 120;
    public static final int START_MANUAL_REPOSITION = 121;
    public static final int START_AUTO_REPOSITION = 122;
    public static final int STOP_MANUAL_REPOSITION = 123;
    public static final int STOP_AUTO_REPOSITION = 124;

    public static final int REPOSITION_START = 0;
    public static final int REPOSITION_SUCCEED = 1;
    public static final int REPOSITION_FAILURE = 2;
    public static final int REPOSITION_EXIT = 3;

    //
    public static final String HOME_PACKAGE_NAME = "com.ainirobot.home";
    public static final String HOME_SERVICE_NAME = "com.ainirobot.home.service.ModuleService";
    public static final String HOME_LAUNCHER_CLASSNAME = "com.ainirobot.home.LaunchActivity";
    public static final String HOME_LAUNCHER_COMPONENT = "com.ainirobot.home/.LaunchActivity";

    public static final String SETTINGS_PACKAGE_NAME = "com.ainirobot.settings";
    public static final String SETTINGS_WIFI_CLASS_NAME = "com.ainirobot.settings.FirstSetWifiSettingActivity";
    public static final String SETTINGS_WIFI_CLASS_ACTION = "com.ainirobot.settings.firstconfig";

    public static final String BROAD_CAST_FIRST_CONFIG_FINISH = "com.ainirobot.firstconfig.config_finish";
    public static final String ANDROID_LAUNCHER_PACKAGE = "com.android.launcher3";


    public static final int OPEN_FRAGMENT_SHOW_INSPECTING = 100;
    public static final int OPEN_FRAGMENT_SHOW_QRCODE = 101;
    public static final int OPEN_FRAGMENT_SHOW_QRCODE_INVALID = 102;
    public static final int OPEN_FRAGMENT_BIND_SUCCESS = 103;
    public static final int OPEN_FRAGMENT_LOGIN_FAIL = 104;
    public static final int OPEN_FRAGMENT_SHOW_INNERERROR = 105;
    public static final int OPEN_FRAGMENT_SET_LANGUAGE = 106;
    public static final int OPEN_FRAGMENT_SET_TIME_ZONE = 107;
    public static final int OPEN_FRAGMENT_SET_AREA_ZONE = 108;
    public static final int OPEN_FRAGMENT_NET_OK = 109;
    public static final int OPEN_FRAGMENT_CLOUD_SERVER = 110;
    public static final int OPEN_FRAGMENT_SETTINGS_SCENARIO_LIST = 111;
    public static final int OPEN_FRAGMENT_SET_OS_TYPE = 112;

    //EventBus UI
    public static final String EVENTBUS_TAG_INSPECTION_FINISH = "eventbus_tag_inspection_finish";
    public static final String EVENTBUS_TAG_SWITCH_FRAGMENT = "eventbus_tag_switch_fragment";

    //EventBus Service
    public static final String EVENTBUS_TAG_SYSTEM_API_CONNECT = "eventbus_tag_system_api_connect";
    public static final String EVEVTBUS_TAG_INIT_FINISH = "eventbus_tag_init_finish";
    public static final String EVENTBUS_TAG_REFRESH_QRCODE = "eventbus_tag_refresh_qrcode";
    public static final String EVENTBUS_TAG_START_CHECKING = "eventbus_tag_start_checking";
    public static final String EVENTBUS_TAG_NEW_REQUEST = "eventbus_tag_new_request";
    public static final String EVENTBUS_TAG_FIRST_CONFIG_FINISH = "eventbus_tag_first_config_finish";
    public static final String EVENTBUS_TAG_CONFIG_NET = "eventbus_tag_config_net";
    public static final String EVENTBUS_TAG_FINISH_ACTIVITY = "eventbus_tag_finish_activity";
    public static final String EVENTBUS_TAG_BACK_PRE_PAGE = "eventbus_tag_back_pre_page";
    public static final String EVENTBUS_TAG_CHECK_INSPECTION_VIEW_MODEL = "eventbus_tag_check_inspection_view_model";
    public static final String EVENTBUS_TAG_REFRESH_SETTINGS_SCENARIO_LIST = "eventbus_tag_refresh_settings_scenario_list";
    public static final String EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO = "eventbus_tag_upload_settings_scenario";
    public static final String EVENTBUS_TAG_UPDATE_SETTINGS_SCENARIO = "eventbus_tag_update_settings_scenario";

    //EventBus: ViewModel
    public static final String EVENTBUS_TAG_INSPECTION_SUCCESS = "eventbus_tag_inspection_success";
    public static final String EVENTBUS_TAG_QRCODE_OR_BINDCODE_INVALIDED = "eventbus_tag_qrcode_or_bindcode_invalided";
    public static final String EVENTBUS_TAG_SERVER_LANGUAGE_VERIFY = "eventbus_tag_server_language_verify";
    public static final String EVENTBUS_TAG_SETTINGS_SCENARIO_LIST = "eventbus_tag_settings_scenario_list";
    public static final String EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO_RESULT = "eventbus_tag_upload_settings_scenario_result";
    public static final String EVENTBUS_TAG_SETTINGS_SCENARIO_INFO = "eventbus_tag_settings_scenario_info";

    public static final int WIFI_REQUEST_CODE = 110;

    public static final long SECOND = 1000L;
    public static final long MINUTE = 1000 * 60L;
    public static final long HOUR = 1000 * 60 * 60L;
    public static final long DAY = 1000 * 60 * 60 * 24L;
    public static final long WEEK = DAY * 7;
    public static final long MONTH = WEEK * 4;
    public static final String DEFAULT_SSID = "unknown_ssid";

    public static final String FIRSTCONFIG_ACTION_WIFI_BACK = "com.ainirobot.firstconfig.action_wifi_back";
    public static final String FIRSTCONFIG_ACTION_WIFI_NEXT = "com.ainirobot.firstconfig.action_wifi_next";
    public static final String FIRST_CONFIG_ACTION_SELF_INSPECT = "first_config_action_self_inspect";

    //TEMP ERR MSG & CODE
    public static final int DEBUG_ERR_CODE_CHECKING_100TIMES = 0x10000;

    public static final int DEBUG_ERR_CODE_QRCODE_PARSER_FAIL = 0x10001;

    public static final int DEBUG_ERR_CODE_BIND_STATE_UNKNOWN = 0x10002;

    public static final int DEBUG_ERR_CODE_BIND_STATE_NULL = 0x10003;


    /**
     * 招财豹开机自检、定时回充、OTA自检失败，自动重试2次重启。
     * 是否可以执行自动重启重试，默认是0,自检正常需要将该值
     */
    public static final int SETTING_CAN_REBOOT_ROBOT = 0;
    public static final int SETTING_CANNOT_REBOOT_ROBOT = 1;
    public static final int SETTING_CAN_REBOOT_ROBOT_COUNT = 2;
}
