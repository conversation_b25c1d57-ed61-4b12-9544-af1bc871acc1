package com.ainirobot.firstconfig.ui.fragment;

import android.os.Bundle;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.databinding.LoginErrorBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.mvvm.vm.InnerErrorViewModel;
import com.ainirobot.firstconfig.utils.ResUtil;


public class LoginErrorFragment extends BaseFragment<InnerErrorViewModel, LoginErrorBinding> {

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_login_error;
    }

    @Override
    protected void initData() {
        Bundle bundle = getArguments();
        if (bundle != null){
            String errorMsg = bundle.getString(FirstConfigDef.BUNDLE_IS_LOGIN_ERROR, "");
            if (Definition.REMOTE_LOGIN_HWID_ERROR.equals(errorMsg)){
                mBinding.loginErrorMsg.setText(ResUtil.getString(R.string.login_error_hwid_error));
            }else if (Definition.REMOTE_LOGIN_ROBOT_NOT_EXIST.equals(errorMsg)){
                mBinding.loginErrorMsg.setText(ResUtil.getString(R.string.login_error_robot_not_exist));
            }
        }
    }

}
