/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.content.Intent;
import android.util.Log;

import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.ServerLanguageBean;
import com.ainirobot.firstconfig.databinding.LoginLoadingBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.BindLoadingViewModel;
import com.ainirobot.firstconfig.ui.view.CloudDialog;
import com.ainirobot.firstconfig.ui.view.LanguageDismatchDialog;
import com.ainirobot.firstconfig.utils.ResUtil;

import androidx.lifecycle.Observer;

/**
 * 登录中加载
 */
public class BindLoadingFragment extends BaseFragment<BindLoadingViewModel, LoginLoadingBinding> {

    private LanguageDismatchDialog mDialog = null;

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_bind_loading;
    }

    @Override
    protected void initData() {
        mBinding.setClickListener(this);
        mViewModel.getLanguageVerifyState().observe(this, bean -> {
            handleVerifyResult(bean);
        });
    }

    private void handleVerifyResult(ServerLanguageBean bean) {
        Log.d(TAG, "handleVerifyResult: " + bean.toString());
        if (bean.isVerified()) {
            cancelDialog();
        } else {
            showDismatchDialog(bean.getServerMain());
        }
    }

    private void showDismatchDialog(String serverMain) {
        if (mDialog != null && mDialog.isShowing()) {
            return;
        }
        mDialog = new LanguageDismatchDialog(getActivity());
        mDialog.setDialogClickListener(new LanguageDismatchDialog.ClickListener() {

            @Override
            public void onConfirmClick() {
                reboot();
            }

            @Override
            public void onCancelClick() {

            }
        });
        mDialog.show();
        mDialog.setContent(ResUtil.getString(R.string.server_language_error,serverMain));
    }

    private void cancelDialog() {
        if (mDialog != null && mDialog.isShowing()) {
            mDialog.dismiss();
            mDialog = null;
        }
    }

    private void reboot() {
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
    }
}
