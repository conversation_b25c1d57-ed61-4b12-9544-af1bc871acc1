/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.ui.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.databinding.InnerErrorBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.InnerErrorViewModel;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;
import com.ainirobot.firstconfig.utils.WeakHandler;

import java.lang.reflect.Method;


public class InnerErrorFragment extends BaseFragment<InnerErrorViewModel, InnerErrorBinding> {
    private static final String TAG = InnerErrorFragment.class.getSimpleName();
    private static final int MSG_QR_CODE_RREATED = 101;

    private MyWeakHandler mHandler;

    private String mErrMsg;
    private String mErrId;

    class MyWeakHandler extends WeakHandler {
        private MyWeakHandler(Activity ref) {
            super(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (super.conflict())
                return;

            if (msg.what == MSG_QR_CODE_RREATED) {
                Bitmap bitmap = (Bitmap) msg.obj;
                if (bitmap != null) {
                    mBinding.ivQrCode.setImageBitmap(bitmap);
                }
            }
        }
    }

    @Override
    protected int getLayoutID() {
        return R.layout.fragment_common_error;
    }

    @Override
    protected void initData() {
        mHandler = new MyWeakHandler(getActivity());

        Bundle bundle = getArguments();
        int code = bundle.getInt(Definition.REMOTE_ERRNO);
        mErrMsg = bundle.getString(Definition.REMOTE_ERRMSG);
        mErrId = bundle.getString(Definition.REMOTE_ERRID);

        Log.e(TAG, "code:" + code + ", msg:" + mErrMsg + ", id:" + mErrId);
        String sn = getResources().getString(R.string.inspect_err_robot_sn) + RobotSettings.getSystemSn();
        mBinding.tvIdSn.setText(sn);
    }

    @Override
    public void onStart() {
        super.onStart();

        showInspectErrorView();
    }


    public void showInspectErrorView() {
        mBinding.inspectionErrorLayout.setVisibility(View.VISIBLE);

        if (!TextUtils.isEmpty(mErrMsg)) {
            mBinding.inspectErrorMessage.setText(mErrMsg);
        }
        if (!TextUtils.isEmpty(mErrId)) {
            showQrCodeUI(mErrId);
            if (!ProductInfo.isOverSea() && !SystemUtils.isLanguageOverseas()){
                createQrCode(getResources().getString(R.string.inspect_err_navigation_log_id)+ mErrId);
            }
        }

        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            mBinding.inspectErrorShutdown.setVisibility(View.VISIBLE);
            mBinding.inspectErrorShutdown.setOnClickListener(v -> {
                try {
                    shutdown("inspect error shutdown!!!");
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            });
        } else {
            mBinding.inspectErrorShutdown.setVisibility(View.GONE);
        }

        mBinding.buttLxcRepair.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //跳转LXC修复页面
                naviToLxcRepair();
            }
        });
    }

    private void showQrCodeUI(String errorId) {
        String err = getResources().getString(R.string.inspect_err_navigation_log_id) + errorId;
        mBinding.tvIdSn.setText(err);
        if (!ProductInfo.isOverSea() && !SystemUtils.isLanguageOverseas()) {
            mBinding.ivQrCode.setVisibility(View.VISIBLE);
            mBinding.tvQrCodeTips.setVisibility(View.VISIBLE);
            mBinding.tvQrCodeTips.setText(getResources().getString(R.string.inspect_err_qr_code_tips_saiph));
            mBinding.tvFeedbackTips.setVisibility(View.GONE);
        }
    }

    private void createQrCode(final String content) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Bitmap centerLogo = ResUtil.getCenterLogoBitmap(R.drawable.first_qrcode_logo);
                Bitmap qrImageBitmap = ResUtil.createQRImage(content, 148, 148,
                        "1", centerLogo);
                if (mHandler != null) {
                    Message message = Message.obtain();
                    message.what = MSG_QR_CODE_RREATED;
                    message.obj = qrImageBitmap;
                    mHandler.sendMessage(message);
                }
            }
        }).start();
    }

    private int shutdown(String reason) throws RemoteException {

        try {
            Class<?> serviceManager = Class.forName("android.os.ServiceManager");
            Method getService = serviceManager.getMethod("getService", String.class);
            Object remoteService = getService.invoke(null, Context.POWER_SERVICE);
            Class<?> stub = Class.forName("android.os.IPowerManager$Stub");
            Method asInterface = stub.getMethod("asInterface", IBinder.class);
            Object powerManager = asInterface.invoke(null, remoteService);
            Method shutdown = powerManager.getClass().getDeclaredMethod("shutdown",
                    boolean.class, String.class, boolean.class);
            shutdown.invoke(powerManager, false, reason, true);
        } catch (Exception e) {
            //nothing to do
        }

        return 0;
    }

    /**
     * 跳转到Lxc修复Activity
     */
    private void naviToLxcRepair() {
        Log.d(TAG,"naviToLxcRepair");
        Intent intent = new Intent();
        intent.setClassName("com.ainirobot.settings", "com.ainirobot.settings.activity.LxcRepairActivity");
        startActivity(intent);
    }
}
