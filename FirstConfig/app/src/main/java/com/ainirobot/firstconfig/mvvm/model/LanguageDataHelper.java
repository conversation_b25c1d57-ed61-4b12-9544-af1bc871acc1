package com.ainirobot.firstconfig.mvvm.model;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Orion on 2020/12/4.
 */
public class LanguageDataHelper {
    private static final String TAG = FirstConfigDef.FLAG + "LanguageDataHelper";

    private static final Uri DATA_URI = Uri.parse("content://com.ainirobot.coreservice.LanguageProvider/" + LanguageField.TABLE_NAME);
    private Context mContext;

    private static class SingleTone {
        private static LanguageDataHelper instance = new LanguageDataHelper();
    }

    public static LanguageDataHelper getInstance() {
        return SingleTone.instance;
    }

    private LanguageDataHelper() {
        this.mContext = FirstConfigApplication.getAppContext();
    }

    public String getLanguageName(String langCode) {
        String langName = "";
        ContentResolver resolver = mContext.getContentResolver();
        Cursor cursor = resolver.query(DATA_URI, new String[]{LanguageField.LANG_NAME},
                LanguageField.LANG_CODE + "=?", new String[]{langCode}, null);
        if (cursor != null && cursor.moveToNext()) {
            langName = cursor.getString(cursor.getColumnIndex(LanguageField.LANG_NAME));
        }
        Log.d(TAG, "getLanguageName: langName=" + langName);
        return langName;
    }


    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public void registerListener(String key, ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                Uri.withAppendedPath(getContentUri(), key), false, contentObserver);
    }

    public void unRegisterListener(ContentObserver contentObserver) {
        mContext.getContentResolver().unregisterContentObserver(contentObserver);
    }

    private Uri getContentUri(){
        return DATA_URI;
    }


    /**
     * 本地和服务端支持列表的交集
     *
     * @return
     */
    public List<LanguageBean> getLocalAndServerSupportLanguageList() {
        List<LanguageBean> languageBeans = getAllLanguageList();
        if (languageBeans == null || languageBeans.size() <= 0) {
            Log.e(TAG, "getLocalAndServerSupportLanguageList: Data null error");
            return null;
        }
        List<LanguageBean> languageLocalAndServer = new ArrayList<>();
        for (LanguageBean bean : languageBeans) {
            if (bean.isSupportLocalAndServer()) {
                languageLocalAndServer.add(bean);
            }
        }
        Log.d(TAG, "getLocalAndServerSupportLanguageList: languageLocalAndServer="
                + languageLocalAndServer.toString());
        return languageLocalAndServer;
    }

    public List<LanguageBean> getAllLanguageList() {
        List<LanguageBean> langInfos = new ArrayList<>();
        ContentResolver resolver = this.mContext.getContentResolver();
        Cursor cursor = resolver.query(DATA_URI, null, null, null, null, null);
        if (cursor == null) {
            return langInfos;
        }
        Log.d(TAG, "getAllLanguageList: cursor size=" + cursor.getCount());
        while (cursor.moveToNext()) {
            langInfos.add(cursorToBean(cursor));
        }
        cursor.close();
        Log.d(TAG, "getAllLanguageList: langInfos=" + langInfos.toString());
        return langInfos;
    }

    /**
     * 恢厂后的首次配置获取LocalLanguageList
     * @return
     */
    public List<LanguageBean> getLocalLanguageList() {
        List<LanguageBean> localList = new ArrayList<>();
        List<LanguageBean> allLanguageList = getAllLanguageList();
        for (LanguageBean bean : allLanguageList) {
            if (bean.isLocalSupport()) {
                localList.add(bean);
            }
        }
        Log.d(TAG, "getLocalLanguageList: " + localList);
        return localList;
    }

    @SuppressLint("LongLogTag")
    private LanguageBean cursorToBean(Cursor cursor) {
        if (cursor == null) {
            Log.e(TAG, "cursorToBean: Cursor null error");
            return null;
        }
        LanguageBean languageBean = new LanguageBean();
        languageBean.setLangCode(cursor.getString(cursor.getColumnIndex(LanguageField.LANG_CODE)));
        languageBean.setLangName(cursor.getString(cursor.getColumnIndex(LanguageField.LANG_NAME)));
        languageBean.setIsDefault(cursor.getInt(cursor.getColumnIndex(LanguageField.IS_DEFAULT)));
        languageBean.setSupport(cursor.getInt(cursor.getColumnIndex(LanguageField.SUPPORT)));
        return languageBean;
    }

    public static final class LanguageField implements BaseColumns {
        static final String TABLE_NAME = "language_list";

        static final String LANG_CODE = "langCode";
        static final String LANG_NAME = "langName";
        static final String IS_DEFAULT = "isDefault";
        static final String SUPPORT = "support";
    }

//    public static class LanguageBean {
//
//        private String langCode;
//        private String langName;
//        private int isDefault = UseState.NOT_DEFAULT;
//        private int support = SupportState.NOT_SUPPORT;
//
//        public static class UseState {
//            public static final int NOT_DEFAULT = 0; //非默认语言
//            public static final int DEFAULT = 1; //默认语言
//        }
//
//        public static class SupportState {
//            public static final int NOT_SUPPORT = 0; //不支持的系统语言
//            public static final int LOCAL = 1 << 0; //本地支持-1
//            public static final int SERVER = 1 << 1; //服务端支持-2
//            public static final int LOCAL_AND_SERVER = LOCAL | SERVER; //本地和服务端都支持-3
//        }
//
//        public String getLangCode() {
//            return langCode;
//        }
//
//        public void setLangCode(String langCode) {
//            this.langCode = langCode;
//        }
//
//        public String getLangName() {
//            return langName;
//        }
//
//        public void setLangName(String langName) {
//            this.langName = langName;
//        }
//
//        public int getIsDefault() {
//            return isDefault;
//        }
//
//        public void setIsDefault(int isDefault) {
//            this.isDefault = isDefault;
//        }
//
//        public boolean isDefaultLanguage() {
//            return this.isDefault == UseState.DEFAULT;
//        }
//
//        public int getSupport() {
//            return support;
//        }
//
//        public void setSupport(int support) {
//            this.support = support;
//        }
//
//        /**
//         * 本地支持
//         *
//         * @return
//         */
//        public boolean isLocalSupport() {
//            return ((this.support & SupportState.LOCAL) != 0);
//        }
//
//        /**
//         * 服务端支持
//         *
//         * @return
//         */
//        public boolean isServerSupport() {
//            return ((this.support & SupportState.SERVER) != 0);
//        }
//
//        /**
//         * 本地和服务端都支持的系统语言(交集)
//         *
//         * @return
//         */
//        public boolean isSupportLocalAndServer() {
//            return this.support == SupportState.LOCAL_AND_SERVER;
//        }
//
//        @Override
//        public String toString() {
//            return "LanguageBean{" +
//                    "langCode='" + langCode + '\'' +
//                    ", langName='" + langName + '\'' +
//                    ", isDefault=" + isDefault +
//                    ", support=" + support +
//                    '}';
//        }
//    }

}
