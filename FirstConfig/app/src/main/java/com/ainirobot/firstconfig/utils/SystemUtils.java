/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.utils;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.inputmethod.InputMethodInfo;
import android.view.inputmethod.InputMethodManager;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.control.ime.GboardConfigLoader;
import com.ainirobot.firstconfig.control.ime.GooglePinyinConfigLoader;
import com.ainirobot.firstconfig.control.ime.LoadImeConfigStrategy;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.ui.activity.FirstConfigActivity;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

import androidx.core.view.accessibility.AccessibilityEventCompat;

public class SystemUtils {
    private static final String TAG = SystemUtils.class.getSimpleName();
    private static String serialNum = "";

    public static String getSystemSerialNo() {
        if (!TextUtils.isEmpty(serialNum)) {
            return serialNum;
        }
        serialNum = RobotSettings.getSystemSn();
        return serialNum;
    }

    public static String getLast6SystemSerialNo() {
        if (TextUtils.isEmpty(getSystemSerialNo())) {
            return "";
        } else if (serialNum.length() <= 6) {
            return serialNum;
        } else {
            return serialNum.substring(serialNum.length() - 6);
        }
    }

    /**
     * 海外机器 都要隐藏二维码
     *
     * @return
     */
    public static boolean isUseBindCode() {
        return ProductInfo.isOverSea();
    }

    /**
     * 除中文以外的是 true
     */
    public static boolean isLanguageOverseas() {
        String robotLang = getSystemLanguage();
        Log.d(TAG, "isOverSea robotLang : " + robotLang);
        return !TextUtils.isEmpty(robotLang) && !robotLang.contains("zh");
    }

    private static String getSystemLanguage() {
        String language = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_LANGUAGE);
        Log.d(TAG, "robot language" + language);
        if (TextUtils.isEmpty(language)) {
            language = Locale.SIMPLIFIED_CHINESE.toString();
            Log.d(TAG, "default language:" + language);
        }
        return language;
    }

    /**
     * 重新开启下拉状态栏
     */
    public static void enableStatusBar() {
        Log.d(TAG, "enableStatusBar: ");
        Settings.Global.putInt(FirstConfigApplication.getAppContext().getContentResolver(),
                Settings.Global.DEVICE_PROVISIONED, 1);
        Settings.Secure.putInt(FirstConfigApplication.getAppContext().getContentResolver(),
                "user_setup_complete", 1);
    }

    /**
     * Returns true if the device is upgrading, such as first boot after OTA.
     *
     * @return
     */
    public static boolean isUpgrade() {
        Context context = FirstConfigApplication.getAppContext();
        PackageManager pm = context.getPackageManager();
        try {
            Method method = PackageManager.class.getMethod("isUpgrade");
            Object invoke = method.invoke(pm);
            Log.d(TAG, "isUpgrade invoke :" + invoke);
            return (Boolean) invoke;
        } catch (Throwable e) {
            e.printStackTrace();
            Log.d(TAG, "isUpgrade error :" + e.getLocalizedMessage());
        }
        return false;
    }


    public static void endFirstConfig(String launcherName) {
        Log.d(TAG, "FirstConfigService will end and disable FirstConfig process !");
        Context context = FirstConfigApplication.getAppContext();
        // 由于初始化过程中,禁用掉了急停和battery. 所以endFirstConfig结束时要resetSystemStatus.
        SystemApiProxy.getInstance().resetSystemStatus();

        // 禁用FirstConfigActivity,后续将不再启动
        disableAndSetupDefaultLauncher(launcherName, context);
    }

    public static void disableAndSetupDefaultLauncher(String launcherName, Context context) {
        PackageManager pm = context.getPackageManager();
        pm.setComponentEnabledSetting(new ComponentName(context, FirstConfigActivity.class),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);

        if (FirstConfigDef.HOME_LAUNCHER_CLASSNAME.equals(launcherName)) {
            Intent intent = pm.getLaunchIntentForPackage(FirstConfigDef.HOME_PACKAGE_NAME);
            try {
                String realLauncherName = intent.getComponent().getClassName();
                Log.d(TAG, "endFirstConfig realLauncherName:" + realLauncherName);
                setupDefaultLauncher(pm, FirstConfigDef.HOME_PACKAGE_NAME, realLauncherName);
            } catch (NullPointerException e) {
                e.printStackTrace();
                setupDefaultLauncher(pm, FirstConfigDef.HOME_PACKAGE_NAME, launcherName);
            }
        } else {
            Log.d(TAG, "endFirstConfig not find the Home , so set ANDROID_LAUNCHER_PACKAGE");
            setupDefaultLauncher(pm, FirstConfigDef.ANDROID_LAUNCHER_PACKAGE, launcherName);
        }
    }

    /**
     * 反射 replacePreferredActivity　设置下次开机启动的默认的launcher
     *
     * @param pm
     * @param launcherPackage
     * @param launcherName
     */
    public static void setupDefaultLauncher(PackageManager pm, String launcherPackage, String launcherName) {
        Intent queryIntent = new Intent();
        queryIntent.addCategory("android.intent.category.HOME");
        queryIntent.setAction("android.intent.action.MAIN");
        queryIntent.addCategory("android.intent.category.DEFAULT");
        List<ResolveInfo> homeActivities = pm.queryIntentActivities(queryIntent, 0);
        if (homeActivities != null) {
            ComponentName defaultLauncher = new ComponentName(launcherPackage, launcherName);
            Log.d(TAG, "defaultLauncher componentName : " + defaultLauncher);
            int activityNum = homeActivities.size();
            ComponentName[] set = new ComponentName[activityNum];
            int defaultMatch = -1;
            for (int i = 0; i < activityNum; i++) {
                ResolveInfo info = homeActivities.get(i);
                Log.d(TAG, "setupDefaultLauncher info.activityInfo.packageName :"
                        + info.activityInfo.packageName + ",activityInfo:" + info.activityInfo.name);
                set[i] = new ComponentName(info.activityInfo.packageName, info.activityInfo.name);
                if (Objects.equals(defaultLauncher, set[i])) {
                    defaultMatch = info.match;
                }
            }
            if (defaultMatch != -1) {
                IntentFilter filter = new IntentFilter();
                filter.addAction("android.intent.action.MAIN");
                filter.addCategory("android.intent.category.HOME");
                filter.addCategory("android.intent.category.DEFAULT");
                try {
                    Method method = PackageManager.class.getMethod(
                            "replacePreferredActivity", IntentFilter.class, int.class, ComponentName[].class, ComponentName.class);
                    method.invoke(pm, filter, AccessibilityEventCompat.TYPE_TOUCH_INTERACTION_START, set, defaultLauncher);
                    Log.d(TAG, "setupDefaultLauncher OK:: set=" + Arrays.toString(set)
                            + " defaultMatch=" + defaultMatch + " defaultLauncher=" + defaultLauncher);
                } catch (Throwable e) {
                    e.printStackTrace();
                }
            } else {
                Log.e(TAG, "this launcher component not find");
            }
        }
    }


    public static String getTopActivityName() {
        ActivityManager mAm = (ActivityManager) FirstConfigApplication.getAppContext().getSystemService(Context.ACTIVITY_SERVICE);
        String activity_name = "";
        try {
            activity_name = mAm.getRunningTasks(1).get(0).topActivity.getClassName();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return activity_name;
    }

    public static boolean isServiceRunning(Class cls, Context context) {
        if (cls == null || TextUtils.isEmpty(cls.getName()) || cls.getPackage() == null) {
            return false;
        }

        ActivityManager manager = (ActivityManager) context
                .getSystemService(Context.ACTIVITY_SERVICE);
        try {
            List<ActivityManager.RunningServiceInfo> list = manager
                    .getRunningServices(50);
            if (list != null && list.size() > 0) {
                for (ActivityManager.RunningServiceInfo info : list) {
                    if (cls.getName().equals(info.service.getClassName())
                            && cls.getPackage().getName().contains(info.service.getPackageName())) {
                        return true;
                    }
                }
            }
        } catch (SecurityException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean hasMapTools() {
        isPackageExist("com.ainirobot.maptool");
        return true;
    }

    private static boolean isPackageExist(String packageName) {
        if (TextUtils.isEmpty(packageName)) {
            return false;
        }
        try {
            FirstConfigApplication
                    .getAppContext()
                    .getPackageManager()
                    .getApplicationInfo(packageName, PackageManager.GET_UNINSTALLED_PACKAGES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    public static void startHomeService(int type) {
        PackageManager pm = FirstConfigApplication.getAppContext().getPackageManager();
        ComponentName componentName = new ComponentName(FirstConfigDef.HOME_PACKAGE_NAME,
                FirstConfigDef.HOME_LAUNCHER_CLASSNAME);
        int flag = PackageManager.COMPONENT_ENABLED_STATE_DEFAULT;
        try {
            flag = pm.getComponentEnabledSetting(componentName);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
        Log.d(TAG, "componentName:" + componentName + " enable status:" + flag);
        if (flag != PackageManager.COMPONENT_ENABLED_STATE_DISABLED) {
            Intent moduleIntent = IntentUtil.createExplicitIntent(FirstConfigDef.HOME_PACKAGE_NAME,
                    FirstConfigDef.HOME_SERVICE_NAME,
                    Definition.FIRST_CONFIG_ACTION_COMPLETE);

            moduleIntent.putExtra(Definition.FIRST_CONFIG_END, type);
            FirstConfigApplication.getAppContext().startService(moduleIntent);
        }
    }

    public static void killProcess(int pid) {
        Log.d(TAG, "killProcess");
        android.os.Process.killProcess(pid);
    }

    public static void loadIME() {
        InputMethodManager imm = (InputMethodManager) FirstConfigApplication
                .getAppContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        List<InputMethodInfo> imeList = imm.getInputMethodList();

        if (imeList == null || imeList.isEmpty()) {
            Log.e(TAG, "no support ime");
            return;
        }

        String type = (FirstConfigApplication.getAppContext().getResources().getConfiguration().orientation
                == Configuration.ORIENTATION_LANDSCAPE) ? "land" : "portrait";

        LoadImeConfigStrategy mTempStrategy = null;

        for (InputMethodInfo ime : imeList) {
            String packageName = ime.getPackageName();
            String serviceName = ime.getServiceName();
            Log.d(TAG, "IME: " + packageName + "/" + serviceName);

            if (GboardConfigLoader.IMENAME.equals(packageName)) {
                mTempStrategy = new GboardConfigLoader(type);
                break;
            } else if (GooglePinyinConfigLoader.IMENAME.equals(packageName)) {
                mTempStrategy = new GooglePinyinConfigLoader();
                break;
            }
        }

        final LoadImeConfigStrategy strategy = mTempStrategy;

        new Thread(new Runnable() {
            @Override
            public void run() {
                if (strategy == null) {
                    Log.e(TAG, "strategy is null, input method:" + strategy);
                    return;
                }
                if (!strategy.prepare()) {
                    Log.e(TAG, strategy.getName() + " prepare failed");
                    return;
                }
                Log.d(TAG, "BackupManager thread start to load IME ");
                if (!strategy.load()) {
                    Log.e(TAG, strategy.getName() + " load failed");
                    return;
                }
                Log.d(TAG, "BackupManager thread start to recycle ");
                if (!strategy.recycle()) {
                    Log.e(TAG, strategy.getName() + " recycle failed");
                }
            }
        }, "BackupManager").start();
    }
}
