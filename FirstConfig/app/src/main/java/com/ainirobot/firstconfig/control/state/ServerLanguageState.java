/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.LanguageBean;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.ServerLanguageBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.simple.eventbus.EventBus;

import java.util.List;

/**
 * 激活成功后，获取服务端的主语言,并将其切换为系统语言
 */
public class ServerLanguageState extends BaseState {
    private static final String TAG = ServerLanguageState.class.getSimpleName();
    private Object mData;

    ServerLanguageState(int resId) {
        super(resId);
    }

    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);

        this.mData = data;
        checkRemoteLanguageList();
    }

    private void showBindSuccess(Object data) {
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_BIND_SUCCESS, data),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

    /**
     * 检查是否可以同步服务端的数据库语言
     */
    private void checkRemoteLanguageList() {
        SystemApi.getInstance().registerStatusListener(Definition.ROBOT_LANGUAGE, mStatusListener);
        DelayTask.cancel(TAG);
        DelayTask.submit(TAG, new Runnable() {
            @Override
            public void run() {
                String remoteLanguage = SystemApi.getInstance().checkRemoteLanguage();
                Log.d(TAG, "checkRemoteLanguage list :" + remoteLanguage);
                if(TextUtils.isEmpty(remoteLanguage)){
                    Log.d(TAG, "request again after 2s");
                    return;
                }

                boolean verifyOK = verifyLanguage();
                if (!verifyOK){
                    String mainLanguage = getServerMainLanguage(remoteLanguage);
                    Log.d(TAG, "mainLanguage : "+ mainLanguage);
                    EventBus.getDefault().post(new ServerLanguageBean(verifyOK, mainLanguage), FirstConfigDef.EVENTBUS_TAG_SERVER_LANGUAGE_VERIFY);
                }
                DelayTask.cancel(TAG);

            }
        }, 0, 2 * Definition.SECOND);
    }

    /**
     * 验证服务端的默认主语言
     */
    private boolean verifyLanguage() {
        String languageList = SystemApi.getInstance().getLocalAndServerSupportLanguageList();
        Log.d(TAG, "localAndServerSupportLanguageList list : " + languageList);
        TypeToken<List<LanguageBean>> list = new TypeToken<List<LanguageBean>>() {
        };
        List<LanguageBean> beanList = new Gson().fromJson(languageList, list.getType());
        for (LanguageBean bean : beanList) {
            Log.d(TAG, "verifyLanguage: " + bean);
            if (bean.getIsDefault() == LanguageBean.UseState.DEFAULT) {
                RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_LANGUAGE, bean.getLangCode());//切换为系统语言
                return true;
            }
        }
        return false;
    }

    private String getServerMainLanguage(@NonNull String json) {
        TypeToken<List<LanguageBean>> list = new TypeToken<List<LanguageBean>>() {
        };
        List<LanguageBean> beanList = new Gson().fromJson(json, list.getType());
        for (LanguageBean bean : beanList) {
            if (bean.getIsDefault() == LanguageBean.UseState.DEFAULT) {
                Log.d(TAG, "getServerMainLanguage bean : " + bean.toString());
                return bean.getLangName();
            }
        }
        return "";
    }

    private StatusListener mStatusListener = new StatusListener() {

        @Override
        public void onStatusUpdate(String type, String data) throws RemoteException {
            super.onStatusUpdate(type, data);
            Log.d(TAG, "onStatusUpdate type :" + type + ", data : " + data);
            if (Definition.ROBOT_LANGUAGE.equals(type)) {
                if (ProductInfo.isDeliveryProduct()) {
                    EventBus.getDefault().post(new UIMessage<>(FirstConfigDef.OPEN_FRAGMENT_SETTINGS_SCENARIO_LIST),
                            FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                } else {
                    onSuccess();
                }
            }
        }
    };

    @Override
    protected BaseState getNextState() {
        return StateEnum.INSPECT.getState();
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.CONFIGURE.getState();
    }

    @Override
    protected void exit() {
        SystemApi.getInstance().unregisterStatusListener(mStatusListener);
        super.exit();
    }
}
