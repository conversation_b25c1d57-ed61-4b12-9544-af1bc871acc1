/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.SettingsUtil;
import com.ainirobot.coreservice.client.actionbean.InspectionResult;
import com.ainirobot.firstconfig.FirstConfigApplication;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.GlobalData;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.StateData;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.bi.BiInspectionReport;
import com.ainirobot.firstconfig.control.LampLightManager;
import com.ainirobot.firstconfig.control.SpeechManager;
import com.ainirobot.firstconfig.proxy.ICommandListener;
import com.ainirobot.firstconfig.proxy.SystemApiProxy;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import org.simple.eventbus.EventBus;

import java.util.List;
import java.util.Locale;

/**
 * 自检
 */
public class InspectState extends BaseState {
    private static final String TAG = InspectState.class.getSimpleName();

    private String mErrorId;
    private StringBuilder mErrorMsg;
    private int mLineNum;

    InspectState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
        mErrorId = null;
        mErrorMsg = new StringBuilder();
    }

    @Override
    protected void doAction() {
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SHOW_INSPECTING),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
        SystemApiProxy.getInstance().startInspection(4 * Definition.MINUTE, new ICommandListener() {
            @Override
            public void onResult(int i, String s) {
                handleInspectResult(i,s);
            }

            @Override
            public void onError(int i, String s) {
                Log.e(TAG, "onError i:" + i + ", s:" + s);
                excuteInspectFailRebootRobot();
            }

            @Override
            public void onStatusUpdate(int i, String s) {
                Log.v(TAG, "onStatusUpdate i:" + i + ", s:" + s);
            }
        });
    }

    private void handleInspectResult(int i, String s) {
        Log.d(TAG, "inspect listener onResult == OK ? "
                + (i == Definition.RESULT_OK) + ", curThread = "
                + Thread.currentThread().getName());
        Log.d(TAG, "inspect response = " + s);

        InspectionResult bean = null;
        try {
            bean = new Gson().fromJson(s, InspectionResult.class);

        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }
        boolean status = parseResponse(bean, s);
        if (status) {
            resetAutoRetryRebootValue();
//            onSuccess();
            reportInspectBi(bean, s, BiInspectionReport.ACTION_SUCCESS);
            Log.d(TAG, "handleInspectResult:success: isShowWebViewInspectUI=" +
                    GlobalData.INSTANCE.isShowWebViewInspectUI());
            if (GlobalData.INSTANCE.isShowWebViewInspectUI()) {
                Log.d(TAG, "handleInspectResult:success: Send event to Model!");
                EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_INSPECTION_SUCCESS);
            } else {
                showBindSuccess();
            }
        } else {
            int canReboot = SettingsUtil.getInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                    FirstConfigDef.SETTING_CAN_REBOOT_ROBOT);
            if (canReboot == FirstConfigDef.SETTING_CAN_REBOOT_ROBOT
                    && (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct() || ProductInfo.isMeissa2())){
                reportInspectBi(bean, s, BiInspectionReport.ACTION_FAIL_REBOOT);
            }else {
                reportInspectBi(bean, s, BiInspectionReport.ACTION_FAIL_ALREADY_REBOOT);
            }
            excuteInspectFailRebootRobot();
        }
    }

    @Override
    protected BaseState getNextState() {
        return null;
    }

    @Override
    protected void exit() {
        super.exit();
    }

    private void showBindSuccess() {
        SharedPrefUtil.getInstance().setConfigComplete();
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_BIND_SUCCESS),
                FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

    private boolean parseResponse(InspectionResult bean, String s) {
        Log.d(TAG, "invokeResponse bean:" + bean);
        mErrorId = generateErrorId();

        if (bean == null) {
            return false;
        } else {
            boolean checked = inspectResult(bean);
            int result = bean.getResult();
            Log.d(TAG, "inspectResult = " + result);
            return checked;
        }
    }

    private void reportInspectBi(InspectionResult bean, String inspectResult, int rebootAction) {
        if (bean == null){
            return;
        }
        List<InspectionResult.FailBean> failBeans = bean.getFail();
        boolean isHasFailItem = false;
        if (failBeans != null) {
            isHasFailItem = failBeans.size() > 0;
        }
        boolean isFailItemShow = bean.getResult() != 1;
        int inspectBiType = isFailItemShow ? BiInspectionReport.SHOW_TO_USER
                : BiInspectionReport.NO_SHOW_TO_USER;
        BiInspectionReport biInspectionReport = new BiInspectionReport();
        biInspectionReport.addData(inspectResult)
                .addResult(bean.getResult())
                .addType(isHasFailItem ? inspectBiType : "")
                .addErrorId(isHasFailItem ? mErrorId : "")
                .addAction(rebootAction)
                .report();
    }

    private boolean inspectResult(InspectionResult bean) {
        Log.i(TAG, "inspectResult: " + bean);
        if (bean.getResult() != 1) {
            List<InspectionResult.FailBean> fails = bean.getFail();
            if (fails == null || fails.isEmpty()) {
                return bean.getResult() == 1;
            }
            for (InspectionResult.FailBean fail : fails) {
                if (fail.isIgnore()) {
                    Log.i(TAG, "inspectResult: inspect item ignore:" + fail);
                    continue;
                }
                mErrorMsg.append(formatErrorMsg(fail));
            }
        }
        return bean.getResult() == 1;
    }

    private String formatErrorMsg(InspectionResult.FailBean fail) {
        Context context = FirstConfigApplication.getAppContext();
        //int id = context.getResources().getIdentifier(fail.getErrorMsg(), "string", context.getPackageName());

        String errorMsg = "";
//        String[] supportLanguage = ResUtil.getStringArray(R.array.lang);
//        for (String lang : supportLanguage) {
//            errorMsg += fail.getErrorMsg(lang) + "  ";
//        }
        errorMsg = fail.getErrorMsg();

        if (TextUtils.isEmpty(errorMsg)) {
            return "";
        }
        //String itemErrorMsg = id > 0 ? context.getString(id) : fail.getErrorMsg();
        String msg = String.format(Locale.ENGLISH, "%d.%s\n", ++mLineNum, errorMsg);
        Log.i(TAG, "inspectResult: error msg:" + msg);
        return msg;
    }


    /**
     * 如果首次自检失败，自动重启一次，随后只有自检成功再重置状态。
     */
    private void resetAutoRetryRebootValue(){
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMiniProduct() || ProductInfo.isMeissa2()){
            SettingsUtil.putInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                    FirstConfigDef.SETTING_CAN_REBOOT_ROBOT);
        }
    }

    /**
     * 如果是招财豹，自检失败后自动重启一次
     */
    private void excuteInspectFailRebootRobot(){
        int canReboot = SettingsUtil.getInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                FirstConfigDef.SETTING_CAN_REBOOT_ROBOT);
        if ((ProductInfo.isMiniProduct() && canReboot == FirstConfigDef.SETTING_CAN_REBOOT_ROBOT)
                || ((ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) && canReboot < FirstConfigDef.SETTING_CAN_REBOOT_ROBOT_COUNT)){
            Log.d(TAG, "excuteInspectFailRebootRobot reboot robot!");
            SettingsUtil.putInt(mContext, SettingsUtil.ROBOT_SETTING_INSPECT_FAIL_AUTO_REBOOT,
                    ProductInfo.isMiniProduct() ? FirstConfigDef.SETTING_CANNOT_REBOOT_ROBOT : canReboot + 1);

            Intent intent = new Intent(Intent.ACTION_REBOOT);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
            return;
        }
        Log.d(TAG, "excuteInspectFailRebootRobot show error ui!");
        SpeechManager.getInstance().speechPlayText(ResUtil.getString(R.string.inspect_err_speech_tips));
        InspectState.this.onError(new StateData(0
                , mErrorMsg.toString(), mErrorId, null));
        if (ProductInfo.isDeliveryProduct() || ProductInfo.isMeissaPlus() || ProductInfo.isMeissa2()) {
            if (SystemApiProxy.getInstance().isUseAutoEffectLed()) {
                SystemApiProxy.getInstance().setBottomLedEffect(0, Definition.LED_EFFECT_YELLOWNORMAL, null);
                if (ProductInfo.isSlimProduct()) {
                    SystemApiProxy.getInstance().setTrayLedEffect(0,
                            Definition.LED_EFFECT_YELLOWNORMAL, null);
                }
            } else {
                LampLightManager.getInstance().setLightColor(Integer.parseInt(LampLightManager.YELLOW, 16));
            }
        }
    }
}
