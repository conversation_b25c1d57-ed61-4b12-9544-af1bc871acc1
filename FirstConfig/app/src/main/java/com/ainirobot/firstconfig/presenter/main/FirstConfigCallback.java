/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.presenter.main;

import android.os.Bundle;
import android.util.Log;

import com.ainirobot.coreservice.client.module.ModuleCallbackApi;
import com.ainirobot.firstconfig.FirstConfigDef;

import org.simple.eventbus.EventBus;

public class FirstConfigCallback extends ModuleCallbackApi {
    private static final String TAG = FirstConfigCallback.class.getSimpleName();

    @Override
    public boolean onSendRequest(int reqId, String reqType, String reqText, String reqParam){
        Log.d(TAG, " ====  New request: " + " reqId:" + reqId + " reqType:" + reqType
                + " reqText:" + reqText + " reqParam:" + reqParam);

        Bundle bundle = new Bundle();
        bundle.putInt(FirstConfigDef.BUNDLE_ID, reqId);
        bundle.putString(FirstConfigDef.BUNDLE_INTENT, reqType);
        bundle.putString(FirstConfigDef.BUNDLE_PARAM, reqParam);

        EventBus.getDefault().post(bundle, FirstConfigDef.EVENTBUS_TAG_NEW_REQUEST);
        return true;
    }

    @Override
    public void onHWReport(int hwFunction, String command, String hwReport){
//        Log.d(TAG, "HW report: " + " hwFunction is:" + hwFunction + " command:" + command +
//                " hwReport is:" + hwReport);
    }
}
