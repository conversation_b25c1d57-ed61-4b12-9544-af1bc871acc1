package com.ainirobot.firstconfig.control;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.actionbean.Pose;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;


public class RepositionManager {

    private static final String TAG = RepositionManager.class.getSimpleName();

    private Handler mHandler;
    private RepositionListener mRepositionListener;
    private Pose mChargingPose;
    private boolean mIsRepositing;
    private RepositionState mRepositeState = RepositionState.IDLE;

    private enum RepositionState{
        IDLE, GET_ORIGIN, JUDGE, START, END
    }

    private RepositionManager() {}

    private static volatile RepositionManager mInstance;

    public static RepositionManager getInstance() {
        if (mInstance == null) {
            synchronized (RepositionManager.class) {
                if (mInstance == null) {
                    mInstance = new RepositionManager();
                }
            }
        }
        return mInstance;
    }

    public Handler getRepositionHandler() {
        if (mHandler == null) {
            initHandler();
        }
        return mHandler;
    }

    @SuppressLint("HandlerLeak")
    private void initHandler(){
        this.mHandler = new Handler(){
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                final Bundle bundle = msg.getData();
                Log.d(TAG, "handleMessage  " + msg.what);
                switch (msg.what){
                    case FirstConfigDef.MSG_REPOSITION_GUIDE_CONFIRM:
                        startRetryEstimate();
                        break;
//                    case FirstConfigDef.MSG_REPOSITION_FAILURE_RETRY:
//                        startRetryEstimate();
//                        break;
                    case FirstConfigDef.START_MANUAL_REPOSITION:
                        String params = bundle.getString("params");
                        if (!TextUtils.isEmpty(params) && params.equals("test")){
                            startGetChargingPosition(true);
                        }else {
                            startGetChargingPosition(false);
                        }
                        break;
                    default:
                        break;
                }
            }
        };
    }

    private void startGetChargingPosition(final boolean isTest){
        Log.i(TAG, "get charging position is test:"+isTest);
        if (mRepositeState != RepositionState.IDLE){
            return;
        }

        mRepositeState = RepositionState.GET_ORIGIN;
        SystemApi.getInstance().getLocation(0, Definition.START_CHARGE_PILE_POSE, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.i(TAG, "getLocation result: " + result + " message:  "+message);
                switch (result) {
                    case Definition.RESULT_OK: {
                        try {
                            JSONObject jsonObject = new JSONObject(message);
                            if (null == mChargingPose) {
                                mChargingPose = new Pose();
                            }
                            boolean state = jsonObject.optBoolean(Definition.JSON_NAVI_SITE_EXIST, false);
                            if (!state){
                                readyForExit("");
                            } else {
                                mChargingPose.setX((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_X, 0.0));
                                mChargingPose.setY((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_Y, 0.0));
                                mChargingPose.setTheta((float) jsonObject.optDouble(Definition.JSON_NAVI_POSITION_THETA, 0.0));
                                startRobotEstimate(isTest);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                            readyForExit("");
                        }
                    }
                    break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        readyForExit("");
                        break;
                    default:
                        break;
                }
            }
        });
    }

    private void startRobotEstimate(boolean isTest) {
        Log.i(TAG, "startRobotEstimate is test: "+ isTest);
        if (mRepositeState != RepositionState.GET_ORIGIN){
            return;
        }

        mRepositeState = RepositionState.JUDGE;

        if (isTest) {
            SystemApi.getInstance().isRobotEstimate(0, new CommandListener() {
                @Override
                public void onResult(int result, String message) {
                    super.onResult(result, message);
                    Log.i(TAG, "isRobotEstimate result:" + result + " message:" + message);
                    mRepositeState = RepositionState.START;
                    switch (result) {
                        case Definition.RESULT_OK:
                            if (!TextUtils.isEmpty(message) && "true".equals(message)) {
                                speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_state_ok));
                                if (mRepositionListener != null) {
                                    mRepositionListener.onRepositionStatus(FirstConfigDef.REPOSITION_EXIT, "");
                                }
                            } else {
                                speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_reset_remind_msg));
                            }
                            break;
                        case Definition.RESULT_FAILURE:
                        case Definition.RESULT_STOP:
                            speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_reset_remind_msg));
                            break;
                        default:
                            speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_reset_remind_msg));
                            break;
                    }
                }
            });
        } else {
            mRepositeState = RepositionState.START;
            speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_reset_remind_msg));
        }
    }

    private void startRetryEstimate() {
        if (mIsRepositing){
            Log.i(TAG, "is repositing");
            return;
        }

        if (mRepositionListener == null || mChargingPose == null){
            Log.i(TAG, "mRepositionListener or mchargingPose is null");
            return;
        }

        mIsRepositing = true;

        if (mRepositionListener != null) {
            mRepositionListener.onRepositionStatus(FirstConfigDef.REPOSITION_START, null);
        }

        Gson mGson = new Gson();
        SystemApi.getInstance().setPoseEstimate(0, mGson.toJson(mChargingPose), new CommandListener(){
            @Override
            public void onResult(int result, String message) {
                super.onResult(result, message);
                Log.i(TAG, "setPoseEstimate result:"+result+" message:"+message);
                mIsRepositing = false;
                switch (result){
                    case Definition.RESULT_OK:
                        if (!TextUtils.isEmpty(message) && message.equals("succeed")){
                            repositionSuccess();
                        }else {
                            relocationFailed();
                        }
                        break;
                    case Definition.RESULT_FAILURE:
                    case Definition.RESULT_STOP:
                        relocationFailed();
                        break;
                    default:
                        break;
                }
            }
        });
    }

    private void repositionSuccess() {
        Log.i(TAG, "reposition success");
        speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_reset_success));
        if(mRepositionListener != null){
            mRepositionListener.onRepositionStatus(FirstConfigDef.REPOSITION_SUCCEED, null);
        }
    }

    private void relocationFailed() {
        speechText(ResUtil.getString(com.ainirobot.firstconfig.R.string.reposition_reset_failed));
        if (mRepositionListener != null){
            mRepositionListener.onRepositionStatus(FirstConfigDef.REPOSITION_FAILURE, "");
        }
    }

    private void readyForExit(String playTTS) {
        speechText(playTTS);
        if (mRepositionListener != null){
            mRepositionListener.onRepositionStatus(FirstConfigDef.REPOSITION_FAILURE, "");
        }
    }

    public void registRepositionListener(RepositionListener listener) {
        this.mRepositionListener = listener;
    }

    public void unRegisterRepositionListener(){
        this.mRepositionListener = null;
    }

    private void speechText(String speechMsg) {
        if (!TextUtils.isEmpty(speechMsg)) {
            SpeechManager.getInstance().speechPlayText(speechMsg);
        }
    }

    public interface RepositionListener {
        void onRepositionStatus(int status, String message);
    }
}
