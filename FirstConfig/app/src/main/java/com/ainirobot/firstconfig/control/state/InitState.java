/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.ui.view.MarkView;
import com.ainirobot.firstconfig.utils.SystemUtils;

import org.simple.eventbus.EventBus;

/**
 * 状态机的初始化State
 */
public class InitState extends BaseState {
    private static final String TAG = InitState.class.getSimpleName();

    InitState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);
    }

    @Override
    protected void doAction() {
        onSuccess();
    }

    @Override
    protected BaseState getNextState() {
        return StateEnum.LANGUAGE.getState();
    }

    @Override
    protected void exit() {
        super.exit();
    }

}
