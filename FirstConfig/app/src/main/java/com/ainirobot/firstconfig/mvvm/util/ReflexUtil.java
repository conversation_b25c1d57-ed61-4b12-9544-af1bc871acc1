package com.ainirobot.firstconfig.mvvm.util;

import android.util.Log;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 反射工具类
 * @param <T>
 */
public class ReflexUtil<T> {

    private static final String TAG = "ReflexUtil";

    /**
     * 获取获取clz的直接父类的Type 类型,如果Type是参数化类型,返回第一个泛型参数.
     * 否则返回空
     *
     * @param clz
     * @return
     */
    public Class<T> getParameterizedClz(Class clz) {

        Type type = clz.getGenericSuperclass();//获取clz的直接父类的Type 类型, Type包括原始类型、参数化类型、数组类型、类型变量和基本类型
        ParameterizedType pType = null;
        if (type instanceof ParameterizedType) {
            pType = (ParameterizedType) type;
        } else {
            return null;
        }
        Type[] types = pType.getActualTypeArguments();
        return (Class<T>) types[0];
    }

    /**
     * 尽量返回类型class的clz的实例对象,否则返回空
     *
     * @param clz
     * @return
     */
    public T getObj(Class clz) {
        Class clzz = getParameterizedClz(clz);
        if (clzz != null) {
            try {
                return (T) clzz.newInstance();
            } catch (IllegalAccessException | InstantiationException e) {
                Log.w(TAG, "getObj: " + e.getMessage());
                return null;
            }
        }
        return null;
    }
}
