package com.ainirobot.firstconfig.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.ainirobot.firstconfig.FirstConfigApplication;

public class SharedPrefUtil {

    public static final String WEIXIN_QRCODE_URL = "weixin_qrcode_url";
    public static final String FIRSTCONFIG_LANGUAGE_CODE = "firstconfig_language_code";
    public static final String FIRSTCONFIG_TIME_ZONE_CODE = "firstconfig_time_zone_code";
    public static final String FIRSTCONFIG_AREA_ZONE_CODE = "firstconfig_area_zone_code";
    public static final String FIRSTCONFIG_CONFIG_COMPLETE = "firstconfig_config_complete";
    public static final String FIRSTCONFIG_OS_TYPE = "firstconfig_os_type";
    private final SharedPreferences mySp;

    private SharedPrefUtil(){
        mySp = FirstConfigApplication.getAppContext().getSharedPreferences("mySp", Context.MODE_PRIVATE);
    }
    private static final class InstanceHolder {
        private static final SharedPrefUtil instance = new SharedPrefUtil();
    }
    public static SharedPrefUtil getInstance(){
        return InstanceHolder.instance;
    }

    private void putString(String key , String value ){
        SharedPreferences.Editor editor = mySp.edit();
        editor.putString(key,value).commit();
    }

    private String getString(String key,String defaultValue){
        return mySp.getString(key,defaultValue);
    }

    public String getWxUrl(){
        return getString(WEIXIN_QRCODE_URL,"");
    }

    public void setWxUrl(String url){
        putString(WEIXIN_QRCODE_URL,url);
    }

    /**
     * 缓存FirstConfig 中默认的LanguageCode
     * @return
     */
    public String getFirstConfigLanguageCode(){
        return getString(FIRSTCONFIG_LANGUAGE_CODE,"");
    }

    public String getFirstConfigLanguageCode(String defaultValue){
        return getString(FIRSTCONFIG_LANGUAGE_CODE,defaultValue);
    }

    public void setFirstConfigLanguageCode(String checkedLanguageCode){
        putString(FIRSTCONFIG_LANGUAGE_CODE,checkedLanguageCode);
    }

    public String getTimeZoneCode(){
        return getString(FIRSTCONFIG_TIME_ZONE_CODE,"");
    }

    public void setTimeZoneCode(String timeZoneCode){
        putString(FIRSTCONFIG_TIME_ZONE_CODE,timeZoneCode);
    }

    public String getAreaZoneCode(){
        return getString(FIRSTCONFIG_AREA_ZONE_CODE,"");
    }

    public void setAreaZoneCode(String areaZoneCode){
        putString(FIRSTCONFIG_AREA_ZONE_CODE,areaZoneCode);
    }

    public String getOSType() {
        return getString(FIRSTCONFIG_OS_TYPE, "");
    }

    public void setOSType(String serverType) {
        putString(FIRSTCONFIG_OS_TYPE, serverType);
    }

    public String getConfigComplete() {
        return getString(FIRSTCONFIG_CONFIG_COMPLETE, "");
    }

    public void setConfigComplete() {
        putString(FIRSTCONFIG_CONFIG_COMPLETE, "1");
    }

    public void clearConfigComplete() {
        putString(FIRSTCONFIG_CONFIG_COMPLETE, "");
    }
}
