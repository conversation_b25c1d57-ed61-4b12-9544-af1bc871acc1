package com.ainirobot.firstconfig.proxy;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.StatusListener;

public interface ISystemApiProxy {
    void registerStatusListener(String type, StatusListener listener);
    void unregisterStatusListener(StatusListener listener);
    void startVision();
    void stopVision();
    void getRobotStatus(String statusType, IStatusListener listener);
    void disableEmergency();
    void disableBattery();
    void resetSystemStatus();
    void startInspection(long time, ICommandListener listener);
    void detach();
    String getRobotString(String key);
    void setRobotString(String key, String value);
    int setLambColor(int reqId, int target, int color);
    boolean isUseAutoEffectLed();
    int setBottomLedEffect(int reqId , int defColor, ICommandListener listener);
    int setTrayLedEffect(int reqId , int defColor, ICommandListener listener);
}
