package com.ainirobot.firstconfig.proxy;

import android.os.Build;
import android.util.Log;

import com.ainirobot.coreservice.IStatusListener;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.StatusListener;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.account.AccountApi;
import com.ainirobot.coreservice.client.person.PersonApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.FirstConfigDef;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

import androidx.annotation.RequiresApi;

public class SystemApiProxy implements ISystemApiProxy {

    private static final int reqId = Definition.MODULE_REQ_ID;
    private static final String TAG = "SystemApiProxy";

    private static SystemApiProxy mInstance;
    private SystemApi mSystemApi;
    private AccountApi mAccountApi;
    private PersonApi mPersonApi;
    private ISystemApiProxy mProxy;
    private Map<Method, Object[]> mApiMap;
    private final RobotSettingApi mRobotSettingApi;

    private SystemApiProxy() {
        mSystemApi = SystemApi.getInstance();
        mAccountApi = AccountApi.getInstance();
        mPersonApi = PersonApi.getInstance();
        mRobotSettingApi = RobotSettingApi.getInstance();
        mApiMap = new HashMap<>();
        EventBus.getDefault().register(this);
    }

    public synchronized static ISystemApiProxy getInstance() {
        if (mInstance == null) {
            mInstance = new SystemApiProxy();
        }
        return mInstance.getProxyInner();
    }


    public void registerStatusListener(String type, StatusListener listener) {
        mSystemApi.registerStatusListener(type, listener);
    }

    @Override
    public void unregisterStatusListener(StatusListener listener) {
        mSystemApi.unregisterStatusListener(listener);
    }

    @Override
    public void startVision() {

    }

    @Override
    public void stopVision() {
        mSystemApi.stopVision(0, null);
    }

    @Override
    public void getRobotStatus(String statusType, IStatusListener listener) {
        //TODO
    }


    @Override
    public void disableEmergency() {
        mSystemApi.disableEmergency();
    }

    @Override
    public void disableBattery() {
        mSystemApi.disableBattery();
    }

    @Override
    public void resetSystemStatus() {
        mSystemApi.resetSystemStatus();
    }

    @Override
    public void startInspection(long time, ICommandListener listener) {
        mSystemApi.startInspection(0, time, new CommandListenerProxy(listener));
    }

    @Override
    public void detach() {
        EventBus.getDefault().unregister(this);
        mProxy = null;
        mInstance = null;
    }

    @Override
    public String getRobotString(String key) {
        return  mRobotSettingApi.getRobotString(key);
    }


    @Override
    public void setRobotString(String key, String value) {
        mRobotSettingApi.setRobotString(key, value);
    }

    @Override
    public int setLambColor(int reqId, int target, int color) {
        return mSystemApi.setLambColor(reqId, target, color);
    }

    @Override
    public boolean isUseAutoEffectLed(){
        return mSystemApi.isUseAutoEffectLed();
    }

    /**
     * Pro配置的底盘灯带控制
     * @param reqId
     * @param defColor can be one of starting with Definition.ZCB...  ; eg: ZCB2UARTLED_GREENBREATH , ZCB2UARTLED_BLUENORMAL and so on.
     * @param listener
     * @return
     */
    @Override
    public int setBottomLedEffect(int reqId, int defColor , ICommandListener listener) {
        return mSystemApi.setBottomLedEffect(reqId, defColor , new CommandListenerProxy(listener));
    }

    @Override
    public int setTrayLedEffect(int reqId, int defColor , ICommandListener listener) {
        return mSystemApi.setTrayLedEffect(reqId, defColor , new CommandListenerProxy(listener));
    }

    private ISystemApiProxy getProxyInner() {
        if (mProxy == null) {
            mProxy = (ISystemApiProxy) Proxy.newProxyInstance(getClass().getClassLoader(), getClass().getInterfaces(), new InvocationHandler() {
                @Override
                public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                    if (!SystemApiUtil.isSystemConnected() && !SystemApiUtil.connectSystemApi()) {
                        Log.e(TAG, "systemApi has't connected , method=" + method.getName() + ",args:" + Arrays.toString(args));
                        synchronized (SystemApiProxy.class) {
                            mApiMap.put(method, args);
                        }
                        return null;
                    }
                    return method.invoke(mInstance, args);
                }
            });
        }
        return mProxy;
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Subscriber(tag = FirstConfigDef.EVENTBUS_TAG_SYSTEM_API_CONNECT)
    private void onSystemServiceConnected(boolean isConnected) {
        Log.d(TAG, "event bus ---connect  isConnected :" + isConnected);
        if (isConnected) {
            reInvoke(mApiMap);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    private void reInvoke(Map<Method, Object[]> map) {
        map.forEach(new BiConsumer<Method, Object[]>() {
            @Override
            public void accept(Method method, Object[] args) {
                try {
                    Log.d(TAG, "handleApiConnected reInvoke \"" + method.getName() + "\",because core not connect");
                    Object invoke = method.invoke(mInstance, args);
                } catch (Exception e) {
                    Log.d(TAG, "reInvoke error = " + e.getMessage());
                }
            }
        });
        map.clear();
    }
}
