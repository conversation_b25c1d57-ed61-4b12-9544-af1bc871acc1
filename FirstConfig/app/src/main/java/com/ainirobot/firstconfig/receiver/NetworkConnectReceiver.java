/*
 *
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */
package com.ainirobot.firstconfig.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;
import android.util.Log;

import com.ainirobot.firstconfig.presenter.net.NetManager;


public class NetworkConnectReceiver extends BroadcastReceiver {
    private static final String TAG = NetworkConnectReceiver.class.getSimpleName();

    public static final int CONNECTIVITY_CHANGE = 5;

    @Override
    public void onReceive(Context context, Intent intent) {

        if (intent != null) {
            if (WifiManager.WIFI_STATE_CHANGED_ACTION.equals(intent.getAction())) {
                int wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0);
                Log.d(TAG, "receiver WIFI_STATE_CHANGED_ACTION state : " + wifiState);
                // * @see #WIFI_STATE_DISABLED
                // * @see #WIFI_STATE_DISABLING
                // * @see #WIFI_STATE_ENABLED
                // * @see #WIFI_STATE_ENABLING
                // * @see #WIFI_STATE_UNKNOWN
                NetManager.getInstance().notifyConnectionChanged(wifiState);
            }
            if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction()) ||
                    "android.net.conn.INET_CONDITION_ACTION".equals(intent.getAction())) {
                Log.d(TAG, "receiver CONNECTIVITY_ACTION :" + intent);
                NetManager.getInstance().notifyConnectionChanged(CONNECTIVITY_CHANGE);
            }

        }
    }
}
