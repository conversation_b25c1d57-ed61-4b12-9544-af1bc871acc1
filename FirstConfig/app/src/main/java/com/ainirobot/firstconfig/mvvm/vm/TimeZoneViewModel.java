package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.content.res.Resources;
import android.content.res.XmlResourceParser;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.TimeZoneBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.bean.ZoneEnum;
import com.ainirobot.firstconfig.bi.TimeZoneReport;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;
import com.ainirobot.firstconfig.utils.SystemUtils;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * 时区处理
 */
public class TimeZoneViewModel extends BaseViewModel<BaseModel> {

    private static final String SEPARATOR = ":";
    private static final int ERROR = -999;
    public final int GMT_MAX = 13; // 最大时区时间
    public final int GMT_MIN = -11; // 最小时区时间
    private int currentCacheGmt = 0;
    private String mTimeZoneCode = "";
    private MutableLiveData<List<TimeZoneBean>> timeZoneState = new MutableLiveData<>();
    private MutableLiveData<String> gmtState = new MutableLiveData<>();
    private MutableLiveData<Integer> currentCacheGmtState = new MutableLiveData<>();
    private List<TimeZoneBean> list = new ArrayList<>();

    public TimeZoneViewModel(@NonNull Application application) {
        super(application);
    }


    public MutableLiveData<List<TimeZoneBean>> getTimeZoneState() {
        return timeZoneState;
    }

    public MutableLiveData<String> getGmtState() {
        return gmtState;
    }

    public MutableLiveData<Integer> getCurrentCacheGmtState() {
        return currentCacheGmtState;
    }


    public int getCurrentCacheGmt(){
        return currentCacheGmt;
    }

    public void updateTimeCode(String timeCode) {
        if (TextUtils.isEmpty(timeCode)) {
            Log.e(TAG, "updateTimeCode failed , timeCode is empty !");
            return;
        }
        mTimeZoneCode = timeCode;
        SharedPrefUtil.getInstance().setTimeZoneCode(mTimeZoneCode);
    }


    @Override
    public void onStart() {
        super.onStart();

        initLocalTimeZoneData();

    }

    private void initLocalTimeZoneData() {
        try {
            initTimeZoneCode();
            Log.d(TAG, "timezoneId: " + mTimeZoneCode);

            Resources res = getApplication().getResources();
            XmlResourceParser xrp = res.getXml(R.xml.time_zone);
            while (xrp.getEventType() != XmlResourceParser.END_DOCUMENT) {
                if (xrp.getEventType() == XmlResourceParser.START_TAG) {
                    String name = xrp.getName();
                    if (name.equals("timezone")) {
                        //0，标识id，1标识名称
                        String value0 = xrp.getAttributeValue(0);
                        String value1 = xrp.getAttributeValue(1);

                        TimeZoneBean zoneBean = new TimeZoneBean(value0, value1,
                                value0.equals(mTimeZoneCode));
                        list.add(zoneBean);
                    }
                }
                xrp.next();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            timeZoneState.postValue(list);
        }
        currentCacheGmt = queryGmtByTimeZoneCode(mTimeZoneCode);
        Log.d(TAG, "init currentCacheGmt : " + currentCacheGmt);
        updateCurrentCacheGmt();
    }

    private void updateCurrentCacheGmt() {
        currentCacheGmtState.postValue(currentCacheGmt);
    }

    private void initTimeZoneCode() {
        String zoneCode = SharedPrefUtil.getInstance().getTimeZoneCode();
        if (TextUtils.isEmpty(zoneCode)) {
            String languageCode = SharedPrefUtil.getInstance().getFirstConfigLanguageCode();
            Log.d(TAG, "initTimeZoneCode curLanguageCode :" + languageCode);
            mTimeZoneCode = checkDefaultTimezoneId(languageCode);
        } else {
            mTimeZoneCode = zoneCode;
        }
    }

    private String checkDefaultTimezoneId(String code) {
        return ZoneEnum.getZoneEnumByCodeName(code).timeZone;
    }


    private String queryTimeZoneCodeByGmt(int gmt){
        for (TimeZoneBean bean : list){
            int tmpGmt = parseGMT(bean.getTimeZoneName());
            if (tmpGmt == gmt){
                return bean.getTimeZoneCode();
            }
        }
        return "";
    }

    private String queryTimeZoneNameByGmt(int gmt){
        for (TimeZoneBean bean : list){
            int tmpGmt = parseGMT(bean.getTimeZoneName());
            if (tmpGmt == gmt){
                return bean.getTimeZoneName();
            }
        }
        return "";
    }


    private int queryGmtByTimeZoneCode(String timeZoneCode){
        for (TimeZoneBean bean : list){
            if (bean.getTimeZoneCode().equals(timeZoneCode)){
                return parseGMT(bean.getTimeZoneName());
            }
        }
        return ERROR;
    }

    /**
     * 解析时区 GMT 时间
     *
     * @param value1
     */
    private int parseGMT(@NonNull String value1) {
        try {
            String[] gmt = value1.split(SEPARATOR)[0].split("GMT");
            return Integer.parseInt(gmt[1]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ERROR;
    }

    private void nextGmtTimeZone() {
        if (currentCacheGmt >= GMT_MAX) {
            Log.d(TAG, "nextGmtTimeZone reached max GMT");
            return;
        }
        String s = queryTimeZoneNameByGmt(++currentCacheGmt);
        Log.d(TAG, "nextTimeZone name : " + s + ", currentGmt : "+ currentCacheGmt);
        gmtState.postValue(s);
    }


    private void preGmtTimeZone() {
        if (currentCacheGmt <= GMT_MIN) {
            Log.d(TAG, "preGmtTimeZone reached min GMT");
            return;
        }
        String s = queryTimeZoneNameByGmt(--currentCacheGmt);
        Log.d(TAG, "preGmtTimeZone name : " + s + ", currentGmt : "+ currentCacheGmt);
        gmtState.postValue(s);
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.butt_back:
                if (ProductInfo.isMeissa2() || ProductInfo.isMiniProduct()) {
                    EventBus.getDefault().post(new UIMessage(
                                    ProductInfo.isOverSea() ? FirstConfigDef.OPEN_FRAGMENT_CLOUD_SERVER : FirstConfigDef.OPEN_FRAGMENT_SET_OS_TYPE),
                            FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                } else {
                    EventBus.getDefault().post(new UIMessage(
                                    ProductInfo.isOverSea() ? FirstConfigDef.OPEN_FRAGMENT_CLOUD_SERVER : FirstConfigDef.OPEN_FRAGMENT_SET_LANGUAGE),
                            FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                }
                break;
            case R.id.butt_next:
                Log.d(TAG, "FirstConfig report time zone " + mTimeZoneCode);
                RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_TIME_ZONE_CODE, mTimeZoneCode);
                new TimeZoneReport().addTimeZone(mTimeZoneCode).addWay().report();
                EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SET_AREA_ZONE),
                        FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                break;

            case R.id.nextGmt:
                nextGmtTimeZone();
                break;

            case R.id.preGmt:
                preGmtTimeZone();
                break;

            default:
                break;
        }
    }

}
