package com.ainirobot.firstconfig.mvvm.vm;

import android.app.Application;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.SettingsScenarioList;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.control.state.SettingsScenarioListState;
import com.ainirobot.firstconfig.mvvm.base.BaseModel;
import com.ainirobot.firstconfig.mvvm.base.BaseViewModel;
import com.ainirobot.firstconfig.utils.SharedPrefUtil;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;
import org.simple.eventbus.ThreadMode;

import java.util.List;

public class SettingsScenarioViewModel extends BaseViewModel<BaseModel> {

    private final MutableLiveData<List<SettingsScenarioList.SettingsScenarioListBean>> dataSuccessState = new MutableLiveData<>();
    private final MutableLiveData<Boolean> dataFailedState = new MutableLiveData<>();
    private final MutableLiveData<Boolean> dataUpdateFailedState = new MutableLiveData<>();
    private final MutableLiveData<Boolean> confirmClickState = new MutableLiveData<>();
    private String mPlatformId;
    private boolean isUploadScenarioSuccess;
    private int mScenarioLoadState;

    public SettingsScenarioViewModel(@NonNull Application application) {
        super(application);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
        EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_REFRESH_SETTINGS_SCENARIO_LIST);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void viewClick(int id) {
        super.viewClick(id);
        switch (id) {
            case R.id.navigation_back:
                EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_BACK_PRE_PAGE);
                break;
            case R.id.navigation_confirm:
                confirmClickState.setValue(mScenarioLoadState == SettingsScenarioListState.RESPONSE_SUCCEED);
                if (mScenarioLoadState == SettingsScenarioListState.RESPONSE_SUCCEED) {//成功
                    if (isUploadScenarioSuccess) {//拉取最新配置
                        EventBus.getDefault().post(false, FirstConfigDef.EVENTBUS_TAG_UPDATE_SETTINGS_SCENARIO);
                    } else {//上报场景类型
                        EventBus.getDefault().post(mPlatformId, FirstConfigDef.EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO);
                    }
                } else if (mScenarioLoadState == SettingsScenarioListState.RESPONSE_SUCCEED_EMPTY) {//场景空->跳过
                    EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_UPDATE_SETTINGS_SCENARIO);
                } else {//失败->刷新
                    EventBus.getDefault().post(true, FirstConfigDef.EVENTBUS_TAG_REFRESH_SETTINGS_SCENARIO_LIST);
                }
                break;
        }
    }

    public MutableLiveData<List<SettingsScenarioList.SettingsScenarioListBean>> getDataSuccessState() {
        return dataSuccessState;
    }

    public MutableLiveData<Boolean> getDataUpdateFailedState() {
        return dataUpdateFailedState;
    }

    public MutableLiveData<Boolean> getDataFailedState() {
        return dataFailedState;
    }

    public MutableLiveData<Boolean> getConfirmClickState() {
        return confirmClickState;
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_SETTINGS_SCENARIO_LIST)
    public void onParamsDataLoad(UIMessage<List<SettingsScenarioList.SettingsScenarioListBean>> msg) {
        Log.d(TAG, "onParamsDataLoad : " + msg.type);
        mScenarioLoadState = msg.type;
        if (msg.type == 1) {//加载成功，有数据
            dataSuccessState.postValue(msg.data);
        } else if (msg.type == 2) {//加载成功，数据为空
            dataFailedState.postValue(true);
        } else {//加载失败
            dataFailedState.postValue(false);
        }
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_UPLOAD_SETTINGS_SCENARIO_RESULT)
    public void onParamsDataUpdate(boolean updateSuccess) {
        isUploadScenarioSuccess = updateSuccess;
        dataUpdateFailedState.setValue(updateSuccess);
    }

    @Subscriber(mode = ThreadMode.MAIN, tag = FirstConfigDef.EVENTBUS_TAG_SETTINGS_SCENARIO_INFO)
    public void onSettingsScenarioInfoSuccess(int state) {
        if (state == 1) {
            SharedPrefUtil.getInstance().setConfigComplete();
            reboot();
        } else {
            dataUpdateFailedState.setValue(false);
        }
        Log.e(TAG, "onSettingsScenarioInfoSuccess: " + state);
    }

    public void updatePlatform(String platformId) {
        if (TextUtils.isEmpty(platformId)) {
            Log.e(TAG, "updatePlatform platformId is empty !");
            return;
        }
        this.mPlatformId = platformId;
    }

    private void reboot() {
        Intent intent = new Intent(Intent.ACTION_REBOOT);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        getApplication().startActivity(intent);
    }
}
