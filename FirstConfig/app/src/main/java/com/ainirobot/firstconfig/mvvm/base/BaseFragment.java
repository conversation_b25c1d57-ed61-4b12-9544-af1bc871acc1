package com.ainirobot.firstconfig.mvvm.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.mvvm.util.ReflexUtil;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

public abstract class BaseFragment<VM extends BaseViewModel<BaseModel>, B extends ViewDataBinding>
        extends Fragment implements IFragmentBinding, View.OnClickListener {

    protected final String TAG = FirstConfigDef.FLAG + getClass().getSimpleName();
    protected VM mViewModel;
    protected B mBinding;
    private Bundle bundle;
    private Activity mActivity;


    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        mActivity = (Activity) context;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        bundle = getArguments();
        Log.i(TAG, "onCreate: bundle:" + bundle);

        //　初始化mViewModel
        initViewModel();

    }

    private void initViewModel() {
        ReflexUtil<VM> reflexUtils = new ReflexUtil<>();
        Class<VM> clz = reflexUtils.getParameterizedClz(getClass());
        if (clz != null) {
            mViewModel = new ViewModelProvider(this).get(clz);
        } else {
            mViewModel = (VM) new ViewModelProvider(this).get(BaseViewModel.class);
        }
        Log.d(TAG, "initViewModel: "+mViewModel.getClass());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        View view = super.onCreateView(inflater, container, savedInstanceState);
        if (getLayoutID() == 0) {
//            onBaseReady(savedInstanceState);
            return view;
        }

        if (!isViewBound()) {
            mBinding = DataBindingUtil.inflate(inflater, getLayoutID(), container, false);
        }
        if (mBinding != null) {
            getLifecycle().addObserver(mViewModel);
            view = mBinding.getRoot();
        } else {
            view = inflater.inflate(getLayoutID(), container, false);
        }
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData();
    }

    /**
     * 子类提供有binding的资源ID
     */
    @LayoutRes
    protected abstract int getLayoutID();

    /**
     * data binding 成功之后,初始化数据
     */
    protected abstract void initData();

    /**
     * 是否已经设置bind
     */
    @Override
    public boolean isViewBound() {
        return mBinding != null;
    }

    @Override
    public void onClick(View v) {
        Log.i(TAG, "onClick: ");
        mViewModel.viewClick(v.getId());
    }

    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Log.i(TAG, "onKeyDown: keyCode:" + keyCode + " event:" + event);
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return keyBack();
        }
        return false;
    }

    protected boolean keyBack() {
        Log.i(TAG, "keyBack: ");
        return false;
    }

    protected void finishActivity(){
        if (mActivity != null){
            Log.d(TAG, "finishActivity called ===");
            mActivity.finish();
        }
    }
}
