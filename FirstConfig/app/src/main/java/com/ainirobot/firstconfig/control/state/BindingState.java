/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.control.state;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.SystemApi;
import com.ainirobot.coreservice.client.listener.CommandListener;
import com.ainirobot.coreservice.utils.DelayTask;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.BindState;
import com.ainirobot.firstconfig.bean.StateData;
import com.ainirobot.firstconfig.bean.QrCodeBean;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.utils.ResUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import org.simple.eventbus.EventBus;

public class BindingState extends BaseState {
    private static final String TAG = BindingState.class.getSimpleName();

    private static final Object QRCODE_TAG = new Object();
    private static final Object BIND_TAG = new Object();

    private static final long BIND_STATE_CHECKING_INTERVAL = 5 * 1000;
    private static final int DEFAULT_EXPIRED_TIME = 60 * 1000;

    private BindState mBindState;

    BindingState(int resId) {
        super(resId);
    }

    @Override
    protected void preAction(Object data, IStateListener listener) {
        super.preAction(data, listener);

        mBindState = null;
    }

    @Override
    protected void doAction() {
        Log.d(TAG, "doAction:Check inspect webView!");
        EventBus.getDefault().post(true,
                FirstConfigDef.EVENTBUS_TAG_CHECK_INSPECTION_VIEW_MODEL);
        requestQrCode();
    }

    @Override
    protected BaseState getNextState() {
        return StateEnum.REPORT_OS_TYPE.getState();
    }

    @Override
    protected BaseState getPreState() {
        return StateEnum.CONFIGURE.getState();
    }

    @Override
    protected void exit() {
        super.exit();

        DelayTask.cancel(BIND_TAG);
        DelayTask.cancel(QRCODE_TAG);
    }

    @Override
    protected Object getData() {
        return mBindState;
    }

    private void requestQrCode() {
        SystemApi.getInstance().remoteRequestQrcode(Definition.MODULE_REQ_ID, new CommandListener() {
            @Override
            public void onResult(int result, String message) {
                if (mBindState != null && mBindState.getBindState() == Definition.BIND_SUCCESS) {
                    Log.d(TAG, "mBindState:" + mBindState.getBindState());
                    return;
                }

                Log.d(TAG, "remoteQrcode args = " + result + ", msg = " + message);
                if (!TextUtils.isEmpty(message)) {
                    try {
                        QrCodeBean bean = new Gson().fromJson(message, QrCodeBean.class);
                        if (bean != null && !TextUtils.isEmpty(bean.getData()) && bean.getExspireTime() > 0) {
                            Log.e(TAG, "exspireTime:" + bean.getExspireTime());
                            startQrCodeCheckingTimer(bean.getExspireTime());
                            startBindStateCheckingTimer();
                            EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SHOW_QRCODE, bean),
                                    FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
                        } else {
                            remoteRequestQrcodeFail();
                        }
                    } catch (JsonSyntaxException e) {
                        e.printStackTrace();
                        Log.d(TAG, "remoteQrcode parse error : " + e.getLocalizedMessage());
                        remoteRequestQrcodeFail();
                    }
                } else {
                    remoteRequestQrcodeFail();
                }
            }
        });
    }

    private void remoteRequestQrcodeFail() {
        EventBus.getDefault().post(new UIMessage(FirstConfigDef.OPEN_FRAGMENT_SHOW_QRCODE,
                new QrCodeBean()), FirstConfigDef.EVENTBUS_TAG_SWITCH_FRAGMENT);
    }

    private void startQrCodeCheckingTimer(long delay) {
        DelayTask.cancel(QRCODE_TAG);
        DelayTask.submit(QRCODE_TAG, new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "qrCode or bindCode exspired ");
                DelayTask.cancel(BIND_TAG);
                EventBus.getDefault().post(true,
                        FirstConfigDef.EVENTBUS_TAG_QRCODE_OR_BINDCODE_INVALIDED);
            }
        }, delay * 1000);
    }

    private void startBindStateCheckingTimer() {
        DelayTask.submit(BIND_TAG, new Runnable() {
            @Override
            public void run() {
                String info = SystemApi.getInstance().getBindInfo();
                mBindState = parseRobotBindInfo(info);
                Log.d(TAG, "handleBindInfo bindState:" + mBindState);
                if (mBindState != null) {
                    int state = mBindState.getBindState();
                    if (state == Definition.BIND_SUCCESS || state == Definition.BIND_NONEED) {
                        onSuccess();
                    } else if (state == Definition.BIND_FAILURE) {
                        Log.e(TAG, "not bind");
                    } else {
                        Log.e(TAG, "state err:" + state);
                        //TODO:zhujie
                        onError(new StateData(FirstConfigDef.DEBUG_ERR_CODE_BIND_STATE_UNKNOWN,
                                ResUtil.getString(R.string.debug_err_msg_bind_state_unknown), generateErrorId(), null));
                    }
                } else {
                    Log.e(TAG, "bindState err:" + info);
                    //TODO:zhujie
                    onError(new StateData(FirstConfigDef.DEBUG_ERR_CODE_BIND_STATE_NULL,
                            ResUtil.getString(R.string.debug_err_msg_bind_state_null), generateErrorId(), null));
                }
            }
        }, 0, BIND_STATE_CHECKING_INTERVAL);
    }

    private BindState parseRobotBindInfo(String param) {
        BindState bindState = null;
        if (TextUtils.isEmpty(param)) {
            return null;
        }
        try {
            bindState = new Gson().fromJson(param, BindState.class);
        } catch (JsonSyntaxException | NullPointerException e) {
            e.printStackTrace();
        }
        return bindState;
    }
}
