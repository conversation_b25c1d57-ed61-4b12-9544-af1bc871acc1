package com.ainirobot.firstconfig.bean;

import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SettingsScenarioList {

    @SerializedName("settings_scenario_list")
    private List<SettingsScenarioListBean> settingsScenarioList;

    public List<SettingsScenarioListBean> getSettingsScenarioList() {
        return settingsScenarioList;
    }

    public void setSettingsScenarioList(List<SettingsScenarioListBean> settingsScenarioList) {
        this.settingsScenarioList = settingsScenarioList;
    }

    public static class SettingsScenarioListBean {
        @SerializedName("settings_scenario_id")
        private String settingsScenarioId;
        @SerializedName("settings_scenario")
        private SettingsScenarioBean settingsScenario;
        private boolean isDefault; // true表示 默认是选中的

        public String getSettingsScenarioId() {
            return settingsScenarioId;
        }

        public void setSettingsScenarioId(String settingsScenarioId) {
            this.settingsScenarioId = settingsScenarioId;
        }

        public SettingsScenarioBean getSettingsScenario() {
            return settingsScenario;
        }

        public void setSettingsScenario(SettingsScenarioBean settingsScenario) {
            this.settingsScenario = settingsScenario;
        }

        public boolean isDefault() {
            return isDefault;
        }

        public void setDefault(boolean aDefault) {
            isDefault = aDefault;
        }

        public static class SettingsScenarioBean {
            @SerializedName("settings_scenario_id")
            private String settingsScenarioId;
            @SerializedName("scenario_name")
            private String scenarioName;
            @SerializedName("scenario_name_intl")
            private JsonElement scenarioNameIntl;

            public String getSettingsScenarioId() {
                return settingsScenarioId;
            }

            public void setSettingsScenarioId(String settingsScenarioId) {
                this.settingsScenarioId = settingsScenarioId;
            }

            public String getScenarioName() {
                return scenarioName;
            }

            public void setScenarioName(String scenarioName) {
                this.scenarioName = scenarioName;
            }

            public JsonElement getScenarioNameIntl() {
                return scenarioNameIntl;
            }

            public void setScenarioNameIntl(JsonElement scenarioNameIntl) {
                this.scenarioNameIntl = scenarioNameIntl;
            }
        }
    }
}
