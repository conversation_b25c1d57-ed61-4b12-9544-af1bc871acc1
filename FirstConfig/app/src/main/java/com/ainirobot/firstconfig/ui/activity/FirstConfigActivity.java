/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.firstconfig.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.IntentUtil;
import com.ainirobot.firstconfig.FirstConfigDef;
import com.ainirobot.firstconfig.GlobalData;
import com.ainirobot.firstconfig.R;
import com.ainirobot.firstconfig.bean.QrCodeBean;
import com.ainirobot.firstconfig.bean.StateData;
import com.ainirobot.firstconfig.bean.UIMessage;
import com.ainirobot.firstconfig.databinding.MainBinding;
import com.ainirobot.firstconfig.mvvm.base.BaseActivity;
import com.ainirobot.firstconfig.mvvm.base.BaseFragment;
import com.ainirobot.firstconfig.mvvm.vm.MainViewModel;
import com.ainirobot.firstconfig.service.FirstConfigService;
import com.ainirobot.firstconfig.ui.fragment.BindCodeFragment;
import com.ainirobot.firstconfig.ui.fragment.BindLoadingFragment;
import com.ainirobot.firstconfig.ui.fragment.BindSuccessFragment;
import com.ainirobot.firstconfig.ui.fragment.ConfigLanguageFragment;
import com.ainirobot.firstconfig.ui.fragment.InnerErrorFragment;
import com.ainirobot.firstconfig.ui.fragment.LoginErrorFragment;
import com.ainirobot.firstconfig.ui.fragment.QRcodeFragment;
import com.ainirobot.firstconfig.ui.fragment.SelfInspectionFragment;
import com.ainirobot.firstconfig.ui.fragment.SelfInspectionWebFragment;
import com.ainirobot.firstconfig.ui.fragment.SetAreaZoneFragment;
import com.ainirobot.firstconfig.ui.fragment.SetCloudServerFragment;
import com.ainirobot.firstconfig.ui.fragment.SetOSTypeFragment;
import com.ainirobot.firstconfig.ui.fragment.SettingsScenarioFragment;
import com.ainirobot.firstconfig.ui.fragment.SetTimeZoneFragment;
import com.ainirobot.firstconfig.utils.SystemUtils;

/**
 * 首次开机后作launcher启动,开机引导流程的Activity,
 * 直到首次配置完成后将默认Launcher 启动项赋给Home的LaunchActivity,
 * 然后disable掉FirstConfigActivity,首次配置完成后的后续开机就不再启动
 * com.ainirobot.firstconfig　进程.
 */
public class FirstConfigActivity extends BaseActivity<MainViewModel, MainBinding> {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        View decorView = getWindow().getDecorView();
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        decorView.setSystemUiVisibility(uiOptions);

        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_first_config;
    }

    @Override
    protected void initView() {

    }

    @Override
    protected void initData() {
        if (SystemUtils.isUpgrade()) {  //OTA升级第一次启动
            SystemUtils.disableAndSetupDefaultLauncher(FirstConfigDef.HOME_LAUNCHER_CLASSNAME,this);
            startHomeLauncher();
            finish();
        } else {            // fastboot或者恢复出厂后 启动
            startService(new Intent(this, FirstConfigService.class));

            Log.d(TAG, "initData start FirstConfigService");
            mViewModel.getFragmentState().observe(this, msg -> handleUiMessage(msg));
            mViewModel.getNetData().observe(this, aBoolean -> startFirstSetSettingsWifi());
            mViewModel.getFinishState().observe(this, aBoolean -> finish());
        }
    }

    private void handleUiMessage(@NonNull UIMessage msg) {
        Log.d(TAG, "onSwitchFragment type:" + msg.type);
        Bundle bundle = null;
        switch (msg.type) {
            case FirstConfigDef.OPEN_FRAGMENT_SHOW_INSPECTING:
                Log.d(TAG, "onSwitchFragment:inspect: isShowWebViewInspectUI=" +
                        GlobalData.INSTANCE.isShowWebViewInspectUI());
                if (GlobalData.INSTANCE.isShowWebViewInspectUI()) {
                    replace(new SelfInspectionWebFragment());
                } else {
                    replace(new SelfInspectionFragment());
                }
                break;

            case FirstConfigDef.OPEN_FRAGMENT_SET_LANGUAGE:
                replace(new ConfigLanguageFragment());
                break;

            case FirstConfigDef.OPEN_FRAGMENT_CLOUD_SERVER:
                replace(new SetCloudServerFragment());
                break;

            case FirstConfigDef.OPEN_FRAGMENT_SET_TIME_ZONE:
                replace(new SetTimeZoneFragment());
                break;

            case FirstConfigDef.OPEN_FRAGMENT_SET_AREA_ZONE:
                replace(new SetAreaZoneFragment());
                break;

            case FirstConfigDef.OPEN_FRAGMENT_NET_OK:
                replace(new BindLoadingFragment());
                break;

            case FirstConfigDef.OPEN_FRAGMENT_LOGIN_FAIL:
                LoginErrorFragment loginErrorFragment = new LoginErrorFragment();
                if (msg.data instanceof StateData) {
                    bundle = new Bundle();
                    bundle.putString(FirstConfigDef.BUNDLE_IS_LOGIN_ERROR, ((StateData) msg.data).msg);
                    loginErrorFragment.setArguments(bundle);
                } else {
                    Log.e(TAG, "data err");
                }
                replace(loginErrorFragment);
                break;
            case FirstConfigDef.OPEN_FRAGMENT_BIND_SUCCESS:
                BindSuccessFragment bindSuccessFragment = new BindSuccessFragment();
                replace(bindSuccessFragment);
                break;
            case FirstConfigDef.OPEN_FRAGMENT_SHOW_QRCODE:
                BaseFragment qrcodeFragment = SystemUtils.isUseBindCode() ?
                        new BindCodeFragment() : new QRcodeFragment();
                if (msg.data instanceof QrCodeBean) {
                    bundle = new Bundle();
                    bundle.putString(Definition.QRCODE_DATA, ((QrCodeBean) msg.data).getData());
                    bundle.putString(Definition.BIND_CODE, ((QrCodeBean) msg.data).getBindCode());
                    qrcodeFragment.setArguments(bundle);
                } else {
                    Log.e(TAG, "data err");
                }
                replace(qrcodeFragment);
                break;

            case FirstConfigDef.OPEN_FRAGMENT_SHOW_INNERERROR:
                InnerErrorFragment innerErrorFragment = new InnerErrorFragment();
                if (msg.data instanceof StateData) {
                    bundle = new Bundle();
                    bundle.putInt(Definition.REMOTE_ERRNO, ((StateData) msg.data).code);
                    bundle.putString(Definition.REMOTE_ERRMSG, ((StateData) msg.data).msg);
                    bundle.putString(Definition.REMOTE_ERRID, ((StateData) msg.data).id);
                    innerErrorFragment.setArguments(bundle);
                } else {
                    Log.e(TAG, "data err");
                }
                replace(innerErrorFragment);
                break;
            case FirstConfigDef.OPEN_FRAGMENT_SETTINGS_SCENARIO_LIST:
                replace(new SettingsScenarioFragment());
                break;
            case FirstConfigDef.OPEN_FRAGMENT_SET_OS_TYPE:
                replace(new SetOSTypeFragment());
                break;
            default:
                break;
        }
    }

    /**
     * 启动Settings 初始化联网
     */
    private void startFirstSetSettingsWifi() {
        Log.d(TAG,"startFirstSetSettingsWifi");
        Intent intent = IntentUtil.createExplicitIntent(FirstConfigDef.SETTINGS_PACKAGE_NAME, FirstConfigDef.SETTINGS_WIFI_CLASS_NAME, "");
        intent.addFlags(Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NO_ANIMATION)
                .putExtra("wifi_auto_finish_on_connect", false);
        startActivity(intent);
    }

    /**
     * 启动Home LauncherActivity
     */
    private void startHomeLauncher(){
        Intent intent = IntentUtil.createExplicitIntent(FirstConfigDef.HOME_PACKAGE_NAME, FirstConfigDef.HOME_LAUNCHER_CLASSNAME, "");
        startActivity(intent);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy -------");
    }
}
