<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.ainirobot.firstconfig"
          android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="com.ainirobot.coreservice.LanguageProvider" />

    <application
        android:name=".FirstConfigApplication"
        android:allowBackup="false"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".ui.activity.FirstConfigActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|mcc|mnc|locale|layoutDirection"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:theme="@style/Theme.Start">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.MONKEY" />
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name=".service.FirstConfigService">
        </service>

    </application>

</manifest>